ADD, ALL, ALTER, AND, ANY, ARITH_OVERFLOW, AS, ASC, AT, AU<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
AVG, BEGIN, BETWEEN, BREAK, BROWSE, BULK, BY, CASCADE, CASE, CHAR_CONVERT,
<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>INT, CLOSE, C<PERSON><PERSON><PERSON>RE<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CO<PERSON><PERSON>, CO<PERSON><PERSON><PERSON>, CO<PERSON>IR<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>IN<PERSON>,
CONTINUE, CO<PERSON><PERSON><PERSON><PERSON>, CONVE<PERSON>, COUNT, COUNT_BIG, CREATE, CURRENT, CURSOR, DATABASE, DBCC,
DEALLOCATE, DECLARE, DECRYPT, DEFAULT, <PERSON>LETE, DESC, DETER<PERSON>NIST<PERSON>, DISK, DISTINC<PERSON>, DROP,
DUMMY, DUMP, ELSE, ENCRYPT, END, ENDTRAN, ERRLVL, ERRORDATA, ERROREXIT, ESCAPE,
EXCEPT, EXCLUSIVE, EXEC, EXECUTE, EXISTS, EXIT, <PERSON>XP_ROW_SIZE, <PERSON>XTERNAL, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
FOR, FOREIGN, FROM, GOTO, GRANT, GROUP, HAVING, HOLDLOC<PERSON>, IDENTITY, IDENTITY_GAP,
IDENTITY_START, IF, IN, INDEX, INOUT, INSENSITIVE, INSERT, INSTALL, INTERSECT, INTO,
IS, ISOLATION, JAR, JOIN, KEY, KILL, LEVEL, LIKE, LINENO, LOAD,
LOCK, MATERIALIZED, MAX, MAX_ROWS_PER_PAGE, MIN, MIRROR, MIRROREXIT, MODIFY, NATIONAL, NEW,
NOHOLDLOCK, NONCLUSTERED, NONSCROLLABLE, NON_SENSITIVE, NOT, NULL, NULLIF, NUMERIC_TRUNCATION
