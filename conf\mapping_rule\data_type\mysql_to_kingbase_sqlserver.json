[{"sourceType": {"name": "TINYINT"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "TINYINT", "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "SMALLINT", "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "SMALLINT UNSIGNED"}, "targetType": {"name": "integer"}}, {"sourceType": {"name": "INT", "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "INT UNSIGNED"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "INT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "MEDIUMINT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "INTEGER UNSIGNED"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "MEDIUMINT"}, "targetType": {"name": "integer"}}, {"sourceType": {"name": "MEDIUMINT", "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "MEDIUMINT UNSIGNED"}, "targetType": {"name": "integer"}}, {"sourceType": {"name": "BIGINT", "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "BIGINT UNSIGNED"}, "targetType": {"name": "numeric"}}, {"sourceType": {"name": "BIGINT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 0, "lengthMax": 0}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 1}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 65537}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 65537}}, {"sourceType": {"name": "TINYTEXT"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "MEDIUMTEXT"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "LONGTEXT"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "TINYBLOB"}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "MEDIUMBLOB"}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "LONGBLOB"}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "BINARY", "lengthMin": 1}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "VARBINARY"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "pg_catalog.date"}}, {"sourceType": {"name": "DATETIME", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "timestamp", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "YEAR"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "SET"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "JSON"}, "targetType": {"name": "text"}}]