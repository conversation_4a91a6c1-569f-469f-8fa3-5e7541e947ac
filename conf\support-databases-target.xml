<!-- 配置转换程序支持的数据库列表，不要修改格式 -->
<ArrayList>
    <item>
        <db_type>KINGBASE</db_type>
        <db_version>V9</db_version>
        <port>54321</port>
        <driver>com.kingbase8.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:kingbase8://{0}:{1}/{2}</value>
                <params>
                    <params><name>ApplicationName</name><value>kingbase_transfer</value></params>
                </params>
            </urls>
            <urls>
                <name>UnixDomainSocket</name>
                <value>jdbc:kingbase8:///{2}</value>
                <params>
                    <params><name>socketFactory</name><value>org.newsclub.net.unix.AFUNIXSocketFactory$FactoryArg</value></params>
                    <params><name>socketFactoryArg</name><value>/tmp/.s.KINGBASE.{1}</value></params>
                    <params><name>sslMode</name><value>disable</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>KINGBASE</db_type>
        <db_version>V8R6C7</db_version>
        <port>54321</port>
        <driver>com.kingbase8.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:kingbase8://{0}:{1}/{2}</value>
                <params>
                    <params><name>ApplicationName</name><value>kingbase_transfer</value></params>
                </params>
            </urls>
            <urls>
                <name>UnixDomainSocket</name>
                <value>jdbc:kingbase8:///{2}</value>
                <params>
                    <params><name>socketFactory</name><value>org.newsclub.net.unix.AFUNIXSocketFactory$FactoryArg</value></params>
                    <params><name>socketFactoryArg</name><value>/tmp/.s.KINGBASE.{1}</value></params>
                    <params><name>sslMode</name><value>disable</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>KINGBASE</db_type>
        <db_version>V8R6</db_version>
        <port>54321</port>
        <driver>com.kingbase8.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:kingbase8://{0}:{1}/{2}</value>
                <params>
                    <params><name>ApplicationName</name><value>kingbase_transfer</value></params>
                </params>
            </urls>
            <urls>
                <name>UnixDomainSocket</name>
                <value>jdbc:kingbase8:///{2}</value>
                <params>
                    <params><name>socketFactory</name><value>org.newsclub.net.unix.AFUNIXSocketFactory$FactoryArg</value></params>
                    <params><name>socketFactoryArg</name><value>/tmp/.s.KINGBASE.{1}</value></params>
                    <params><name>sslMode</name><value>disable</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>KINGBASE</db_type>
        <db_version>V8R3</db_version>
        <port>54321</port>
        <driver>com.kingbase83.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:kingbase83://{0}:{1}/{2}</value>
                <params>
                    <params><name>ApplicationName</name><value>kingbase_transfer</value></params>
                    <params><name>stringtype</name><value>unspecified</value></params>
                </params>
            </urls>
            <urls>
                <name>UnixDomainSocket</name>
                <value>jdbc:kingbase83:///{2}</value>
                <params>
                    <params><name>socketFactory</name><value>org.newsclub.net.unix.AFUNIXSocketFactory$FactoryArg</value></params>
                    <params><name>socketFactoryArg</name><value>/tmp/.s.KINGBASE.{1}</value></params>
                    <params><name>sslMode</name><value>disable</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>KINGBASE</db_type>
        <db_version>V7</db_version>
        <port>54321</port>
        <driver>com.kingbase.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:kingbase://{0}:{1}/{2}</value>
            </urls>
            <urls>
                <name>UnixDomainSocket</name>
                <value>jdbc:kingbase:///{2}</value>
                <params>
                    <params><name>socketFactory</name><value>org.newsclub.net.unix.AFUNIXSocketFactory$FactoryArg</value></params>
                    <params><name>socketFactoryArg</name><value>/tmp/.s.KINGBASE.{1}</value></params>
                    <params><name>sslMode</name><value>disable</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>KINGBASE_ADB</db_type>
        <db_version>3</db_version>
        <port>54322</port>
        <driver>org.postgresql.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:postgresql://{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>PHOTONDB</db_type>
        <db_version>V8R6</db_version>
        <port>54321</port>
        <driver>com.photondb.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:photondb://{0}:{1}/{2}</value>
                <params>
                    <params><name>ApplicationName</name><value>kingbase_transfer</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>ORACLE</db_type>
        <db_version>19c</db_version>
        <port>1521</port>
        <driver>oracle.jdbc.OracleDriver</driver>
        <urls>
            <urls>
                <name>ServiceName</name>
                <value>jdbc:oracle:thin:@//{0}:{1}/{2}</value>
            </urls>
            <urls>
                <name>SID</name>
                <value>jdbc:oracle:thin:@{0}:{1}:{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>ORACLE</db_type>
        <db_version>12c</db_version>
        <port>1521</port>
        <driver>oracle.jdbc.OracleDriver</driver>
        <urls>
            <urls>
                <name>ServiceName</name>
                <value>jdbc:oracle:thin:@//{0}:{1}/{2}</value>
            </urls>
            <urls>
                <name>SID</name>
                <value>jdbc:oracle:thin:@{0}:{1}:{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>ORACLE</db_type>
        <db_version>11g</db_version>
        <port>1521</port>
        <driver>oracle.jdbc.OracleDriver</driver>
        <urls>
            <urls>
                <name>ServiceName</name>
                <value>jdbc:oracle:thin:@//{0}:{1}/{2}</value>
            </urls>
            <urls>
                <name>SID</name>
                <value>jdbc:oracle:thin:@{0}:{1}:{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>ORACLE</db_type>
        <db_version>10g</db_version>
        <port>1521</port>
        <driver>oracle.jdbc.OracleDriver</driver>
        <urls>
            <urls>
                <name>ServiceName</name>
                <value>jdbc:oracle:thin:@//{0}:{1}/{2}</value>
            </urls>
            <urls>
                <name>SID</name>
                <value>jdbc:oracle:thin:@{0}:{1}:{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>MYSQL</db_type>
        <db_version>5.5</db_version>
        <port>3306</port>
        <driver>com.mysql.jdbc.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:mysql://{0}:{1}/{2}</value>
                <params>
                    <params><name>zeroDateTimeBehavior</name><value>convertToNull</value></params>
                    <params><name>useCursorFetch</name><value>false</value></params>
                    <params><name>yearIsDateType</name><value>true</value></params>
                    <params><name>tinyInt1isBit</name><value>false</value></params>
                    <params><name>useUnicode</name><value>true</value></params>
                    <params><name>characterEncoding</name><value>utf-8</value></params>
                    <params><name>useSSL</name><value>false</value></params>
                    <params><name>nullCatalogMeansCurrent</name><value>true</value></params>
                    <params><name>autoReconnect</name><value>true</value></params>
                    <params><name>failOverReadOnly</name><value>true</value></params>
                    <params><name>autoReconnectForPools</name><value>true</value></params>
                    <params><name>allowPublicKeyRetrieval</name><value>true</value></params>
                    <params><name>serverTimezone</name><value>GMT%2B8</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>MYSQL</db_type>
        <db_version>5.6</db_version>
        <port>3306</port>
        <driver>com.mysql.cj.jdbc.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:mysql://{0}:{1}/{2}</value>
                <params>
                    <params><name>zeroDateTimeBehavior</name><value>convertToNull</value></params>
                    <params><name>useCursorFetch</name><value>false</value></params>
                    <params><name>yearIsDateType</name><value>true</value></params>
                    <params><name>tinyInt1isBit</name><value>false</value></params>
                    <params><name>useUnicode</name><value>true</value></params>
                    <params><name>characterEncoding</name><value>utf-8</value></params>
                    <params><name>useSSL</name><value>false</value></params>
                    <params><name>nullCatalogMeansCurrent</name><value>true</value></params>
                    <params><name>autoReconnect</name><value>true</value></params>
                    <params><name>failOverReadOnly</name><value>true</value></params>
                    <params><name>autoReconnectForPools</name><value>true</value></params>
                    <params><name>allowPublicKeyRetrieval</name><value>true</value></params>
                    <params><name>serverTimezone</name><value>GMT%2B8</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>MYSQL</db_type>
        <db_version>5.7</db_version>
        <port>3306</port>
        <driver>com.mysql.cj.jdbc.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:mysql://{0}:{1}/{2}</value>
                <params>
                    <params><name>zeroDateTimeBehavior</name><value>convertToNull</value></params>
                    <params><name>useCursorFetch</name><value>false</value></params>
                    <params><name>yearIsDateType</name><value>true</value></params>
                    <params><name>tinyInt1isBit</name><value>false</value></params>
                    <params><name>useUnicode</name><value>true</value></params>
                    <params><name>characterEncoding</name><value>utf-8</value></params>
                    <params><name>useSSL</name><value>false</value></params>
                    <params><name>nullCatalogMeansCurrent</name><value>true</value></params>
                    <params><name>autoReconnect</name><value>true</value></params>
                    <params><name>failOverReadOnly</name><value>true</value></params>
                    <params><name>autoReconnectForPools</name><value>true</value></params>
                    <params><name>allowPublicKeyRetrieval</name><value>true</value></params>
                    <params><name>serverTimezone</name><value>GMT%2B8</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>MYSQL</db_type>
        <db_version>8.0</db_version>
        <port>3306</port>
        <driver>com.mysql.cj.jdbc.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:mysql://{0}:{1}/{2}</value>
                <params>
                    <params><name>zeroDateTimeBehavior</name><value>convertToNull</value></params>
                    <params><name>useCursorFetch</name><value>false</value></params>
                    <params><name>yearIsDateType</name><value>true</value></params>
                    <params><name>tinyInt1isBit</name><value>false</value></params>
                    <params><name>useUnicode</name><value>true</value></params>
                    <params><name>characterEncoding</name><value>utf-8</value></params>
                    <params><name>useSSL</name><value>false</value></params>
                    <params><name>nullCatalogMeansCurrent</name><value>true</value></params>
                    <params><name>autoReconnect</name><value>true</value></params>
                    <params><name>failOverReadOnly</name><value>true</value></params>
                    <params><name>autoReconnectForPools</name><value>true</value></params>
                    <params><name>allowPublicKeyRetrieval</name><value>true</value></params>
                    <params><name>serverTimezone</name><value>GMT%2B8</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2008</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2012</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2014</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2016</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2017</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2019</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>DM</db_type>
        <db_version>7</db_version>
        <port>5236</port>
        <driver>dm.jdbc.driver.DmDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:dm://{0}:{1}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>DM</db_type>
        <db_version>8</db_version>
        <port>5236</port>
        <driver>dm.jdbc.driver.DmDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:dm://{0}:{1}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>POSTGRESQL</db_type>
        <db_version>12</db_version>
        <port>5432</port>
        <driver>org.postgresql.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:postgresql://{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>GAUSSDB</db_type>
        <db_version>12</db_version>
        <port>5432</port>
        <driver>org.postgresql.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:postgresql://{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>GAUSSDB</db_type>
        <db_version>DWS</db_version>
        <port>8000</port>
        <driver>com.huawei.gauss200.jdbc.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:gaussdb://{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>GBASE</db_type>
        <db_version>8sV8</db_version>
        <port>9088</port>
        <driver>com.gbasedbt.jdbc.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:gbasedbt-sqli://{0}:{1}/{2}</value>
                <params>
                    <params>
                        <name>DB_LOCAL</name>
                        <value>zh_CN.utf-8</value>
                    </params>
                    <params>
                        <name>CLIENT_LOCAL</name>
                        <value>zh_CN.utf-8</value>
                    </params>
                    <params>
                        <name>IFX_USE_STRENC</name>
                        <value>true</value>
                    </params>
                    <params>
                        <name>NEWCODESET</name>
                        <value>utf-8,8859-1,819</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>CLICKHOUSE</db_type>
        <db_version>22</db_version>
        <port>8123</port>
        <driver>com.clickhouse.jdbc.ClickHouseDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:clickhouse://{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>DB2</db_type>
        <db_version>8</db_version>
        <port>50000</port>
        <driver>com.ibm.db2.jcc.DB2Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:db2://{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>DB2</db_type>
        <db_version>9</db_version>
        <port>50000</port>
        <driver>com.ibm.db2.jcc.DB2Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:db2://{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>DB2</db_type>
        <db_version>10</db_version>
        <port>50000</port>
        <driver>com.ibm.db2.jcc.DB2Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:db2://{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>DB2</db_type>
        <db_version>11</db_version>
        <port>50000</port>
        <driver>com.ibm.db2.jcc.DB2Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:db2://{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>HIVE</db_type>
        <db_version>3.1.3</db_version>
        <port>10000</port>
        <driver>org.apache.hive.jdbc.HiveDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:hive2://{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>HIVE</db_type>
        <db_version>inceptor</db_version>
        <port>10000</port>
        <driver>org.apache.hive.jdbc.HiveDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:inceptor2://{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>REDIS</db_type>
        <db_version>7.2.3</db_version>
        <port>6379</port>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>{0}:{1}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>KAFKA</db_type>
        <db_version>3</db_version>
        <port>9092</port>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>{0}:{1}</value>
            </urls>
        </urls>
    </item>
</ArrayList>
