logging.config=${config.path}/logback-spring.xml
logging.prefix=kdts
logging.useSlfLog=false
application.version=@project.version@
build.version=@@BUILDVERSION@@
build.version.switch=off

# spring.devtools.restart.enable=false
# web / shell / gui
spring.dts.console=web
http.port=54523
server.servlet.session.cookie.name=JSESSIONID

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=100MB

# jwt token 有效期，单位为毫秒（24小时）
jwt.expiration=86400000

# https 配置
server.port=54524
server.ssl.enabled=true
server.ssl.key-alias=system
server.ssl.key-store=classpath:keystore/kingbase.keystore
server.ssl.key-store-type=JKS
server.ssl.protocol=TLS
server.ssl.key-store-password=K1ngb@se

# 是否使用遗留二进制拷贝签名
jdbcDriver.useLegacyBinaryCopySignature=true

# 目标端是否显示其他数据库类型选项
show.target.other=true

#公共线程池并行数
#commonPoolParallelism=16

# 自定义上传文件位置
fileUploadPath = conf