[{"sourceType": {"name": "geography"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "geometry"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "LONGNVARCHAR"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "TINYINT"}, "targetType": {"name": "NUMBER(3, 0)"}}, {"sourceType": {"name": "SMALLINT"}, "targetType": {"name": "NUMBER(5, 0)"}}, {"sourceType": {"name": "INT"}, "targetType": {"name": "NUMBER(10, 0)"}}, {"sourceType": {"name": "BIGINT"}, "targetType": {"name": "NUMBER(19, 0)"}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "REAL"}, "targetType": {"name": "NUMBER"}}, {"sourceType": {"name": "MONEY"}, "targetType": {"name": "NUMBER"}}, {"sourceType": {"name": "SMALLMONEY"}, "targetType": {"name": "NUMBER"}}, {"sourceType": {"name": "decimal", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "TIME"}, "targetType": {"name": "TIME"}}, {"sourceType": {"name": "DATETIME"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "DATE"}}, {"sourceType": {"name": "DATETIME2"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "DATETIMEOFFSET"}, "targetType": {"name": "TIMESTAMP WITH TIME ZONE"}}, {"sourceType": {"name": "SMALLDATETIME"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "RAW(8)"}}, {"sourceType": {"name": "BIT"}, "targetType": {"name": "NUMBER(1)"}}, {"sourceType": {"name": "BINARY", "lengthMin": 1, "lengthMax": 1999}, "targetType": {"name": "RAW"}}, {"sourceType": {"name": "BINARY", "lengthMin": 2000, "lengthMax": 20000}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "VARBINARY", "lengthMin": 1, "lengthMax": 1999}, "targetType": {"name": "RAW"}}, {"sourceType": {"name": "VARBINARY", "lengthMin": 2000, "lengthMax": 20000}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "image"}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 1999}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 1999}}, {"sourceType": {"name": "CHAR", "lengthMin": 2000, "lengthMax": 20000}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "NCHAR", "lengthMin": 1, "lengthMax": 999}, "targetType": {"name": "NCHAR", "lengthMin": 1, "lengthMax": 999}}, {"sourceType": {"name": "NCHAR", "lengthMin": 1000, "lengthMax": 20000}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 4001, "lengthMax": 40000}, "targetType": {"name": "CLOB", "lengthMin": 4001, "lengthMax": 40000}}, {"sourceType": {"name": "NVARCHAR", "lengthMin": 1, "lengthMax": 3999}, "targetType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 3999}}, {"sourceType": {"name": "NVARCHAR", "lengthMin": 4000, "lengthMax": 40000}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "text"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "NTEXT"}, "targetType": {"name": "NCLOB"}}, {"sourceType": {"name": "xml"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "UNIQUEIDENTIFIER"}, "targetType": {"name": "VARCHAR2(40)"}}, {"sourceType": {"name": "sql_variant"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "sysname"}, "targetType": {"name": "<PERSON><PERSON><PERSON><PERSON>(128)"}}]