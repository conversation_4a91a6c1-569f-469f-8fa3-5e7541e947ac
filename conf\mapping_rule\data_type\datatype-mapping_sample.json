[{"sourceType": {"name": "BFILE"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "timestamp(6)"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 3, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "tinyint"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 3, "scaleMax": 0, "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 4, "precisionMax": 5, "scaleMax": 0}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 4, "precisionMax": 5, "scaleMax": 0, "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 6, "precisionMax": 10, "scaleMax": 0}, "targetType": {"name": "integer"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 6, "precisionMax": 10, "scaleMax": 0, "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 11, "precisionMax": 20, "scaleMax": 0}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 11, "precisionMax": 20, "scaleMax": 0, "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 21, "precisionMax": 38, "scaleMax": 0}, "targetType": {"name": "numeric", "precisionMin": 21, "precisionMax": 38}}, {"sourceType": {"name": "NUMBER", "precisionMin": 21, "precisionMax": 38, "scaleMax": 0, "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 0, "scaleMin": -127, "scaleMax": -127}, "targetType": {"name": "numeric"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 38, "scaleMin": -127, "scaleMax": -1}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 0, "attachNegativeScaleSize": true}}, {"sourceType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 38, "scaleMin": 1, "scaleMax": 38}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 1, "scaleMax": 38}}, {"sourceType": {"name": "NCHAR", "lengthMin": 1, "lengthMax": 1000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 1000}}, {"sourceType": {"name": "NVARCHAR2", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "varchar2", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "varchar2", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "NCLOB"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "RAW", "lengthMin": 16, "lengthMax": 16}, "targetType": {"name": "UUID"}}, {}, null, {"minSourceDbVersion": "5.1", "maxSourceDbVersion": "5.5", "minTargetDbVersion": "V8R6", "maxTargetDbVersion": "V10R10", "schemaIncludes": ["*"], "schemaExcludes": null, "tableIncludes": null, "tableExcludes": null, "columnIncludes": null, "columnExcludes": null, "sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": null, "lengthMax": "255", "precisionMin": null, "precisionMax": null, "scaleMin": null, "scaleMax": null, "autoIncrement": true}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 10, "lengthMax": 200, "precisionMin": null, "precisionMax": null, "scaleMin": null, "scaleMax": null, "attachNegativeScaleSize": false}}]