[{"sourceType": {"name": "TINYINT"}, "targetType": {"name": "tinyint"}}, {"sourceType": {"name": "SMALLINT", "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "SMALLINT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "SMALLINT UNSIGNED"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "TINYINT", "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "INT", "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "INT UNSIGNED"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "INTEGER UNSIGNED"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "INT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "MEDIUMINT"}, "targetType": {"name": "mediumint"}}, {"sourceType": {"name": "MEDIUMINT", "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "MEDIUMINT UNSIGNED"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "MEDIUMINT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "BIGINT", "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "BIGINT UNSIGNED"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "BIGINT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 0, "lengthMax": 0}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 1}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 65537}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 65537}}, {"sourceType": {"name": "TINYTEXT"}, "targetType": {"name": "tinytext"}}, {"sourceType": {"name": "MEDIUMTEXT"}, "targetType": {"name": "mediumtext"}}, {"sourceType": {"name": "LONGTEXT"}, "targetType": {"name": "longtext"}}, {"sourceType": {"name": "TINYBLOB"}, "targetType": {"name": "tinyblob"}}, {"sourceType": {"name": "MEDIUMBLOB"}, "targetType": {"name": "mediumblob"}}, {"sourceType": {"name": "LONGBLOB"}, "targetType": {"name": "longblob"}}, {"sourceType": {"name": "BINARY", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "binary", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "VARBINARY", "lengthMin": 1, "lengthMax": 65532}, "targetType": {"name": "varbinary", "lengthMin": 1, "lengthMax": 65532}}, {"sourceType": {"name": "DATETIME", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "DATETIME", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "YEAR"}, "targetType": {"name": "year"}}, {"sourceType": {"name": "JSON"}, "targetType": {"name": "json"}}]