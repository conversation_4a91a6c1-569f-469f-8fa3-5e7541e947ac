[{"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["FUNCTION"], "regexp": "int\\(\\d+\\)", "replacement": "int", "replaceType": "All"}, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["FUNCTION,PROCEDURE"], "regexp": "(?i)\\sas\\s", "replacement": " ", "replaceType": "All"}]