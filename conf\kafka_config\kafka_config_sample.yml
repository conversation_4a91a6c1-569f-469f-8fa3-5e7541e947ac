# 重试次数，当生产者发送消息到kafka时，如果发送错误，生成者会自动重试。默认为：0，表示不进行重试
retries: 0
# 应答级别，当生产者发送消息到kafka时，需要等待来自服务器的确认，级别包括：0=只管发,1=领导者确认写入日志时，返回成功,-1(all)=所有分区及副本分区都确认后才返回成功
acks: all
# 批量发送大小，将多个消息打包成批量一次性发送，提高性能和吞吐量
batchSize: 16384
# 消息发送者在发送消息之前等待的时间（以毫秒为单位）。默认值为0，表示立即发送消息，如果值大于0，发送者将在指定时间内等待，以收集更多的消息批量发送，提高性能和吞吐量
lingerMs: 1
# 消息压缩类型，可选值：none(不压缩)，gzip、snappy和lz4,压缩消息可以减少网络传输的数据量，提高吞吐，但会增加cpu使用率，通常情况下如果网络宽带受限制，可以启用消息压缩
compressionType: lz4
# 单个请求的最大字节数，默认值：1048576字节（1MB），此参数用于限制生产者发送的单个请求的大小，防止单个请求过大而影响性能或者网络稳定性，如果消息很大，可以适当调整该值
maxRequestSize: 10485760
# 请求超时时间（以毫秒为单位），默认值：30000毫秒（30秒），如果kafka代理在指定时间内未能响应请求，请求将超时并失败，可以根据网络延迟和负载情况适当调整此值
requestTimeoutMs: 60000
# 发送缓冲区的大小（以字节为单位），默认值为33554432（32MB），此参数用于限制生产者在发送消息之前可以使用的内存量，如果生成者速度大于消费者处理速度，缓冲区可能会被填满，产生发送者阻塞，可以根据生成者和代理性能调整此值
bufferMemory: 33554432
# 是否使用登录，如果设置为true，可kafka客户端将尝试使用登录来连接kafka集群，默认值为false
useLogin: false
# 登录方法，指定登录方法，用于身份验证，常见的值包括PLAIN、GSSAPI等
loginMethod:
# 安全协议，指定kafka服务器通信时使用的安全协议，常见值包括：PLAINTEXT,SSL,SASL_PLAINTEXT,SASL_SSL，如果kafka集群启用了SSL或SASL安全协议，需要相应的设置此值
securityProtocol: SASL_PLAINTEXT
# SSL信任库地址，指定SSL信任库的位置，用于验证服务器证书，这是一个文件路径
sslTruststoreLocation:
# SSL信任库密码，指定SSL信任库密码，用于访问信任库中的证书
sslTruststorePassword:
# 客户端配置，指定客户端的其他配置选项，这通常是一个配置文件的路径
clientConfig:
# kerberos配置，指定kerberos配置文件的位置，用于kerberos身份验证
krbConfig:
# 使用zookeeper，指定是否使用Zookeeper连接，常见值为：true，false
usezk: false
# zk url，如果usezk值为true，该值需要设置
zkUrl:
# topic分组名称,此字段值为自定义内容，如果kafka设置了权限内容，本字段的值需要在kafka中存在本分组
groupId: kdts-group
# 默认是否自动提交ack
enableAutoCommit: false
# 配置sasl模式（包含：PLAIN、SCRAM-SHA-256）
saslMechanism: PLAIN
# 是否开启事务，默认开启
ifTransactions: true