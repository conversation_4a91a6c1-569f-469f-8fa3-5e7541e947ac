[{}, null, {"minSourceDbVersion": "12c", "maxSourceDbVersion": "19c", "minTargetDbVersion": "V8R6", "maxTargetDbVersion": "V10R10", "schemaIncludes": ["*"], "schemaExcludes": null, "tableIncludes": null, "tableExcludes": null, "columnIncludes": null, "columnExcludes": null, "columnTypeIncludes": ["NUMBER"], "type": "RegexpReplace", "content": {"regexp": "\"?(?<n1>\\S+)\"?\\.\"?(?<n2>\\S+)\"?\\.\"?(?<n3>NEXTVAL)\"?", "replacement": "${n3}('${n1}.${n2}'::regclass)", "replaceType": "All"}}]