[{"sourceType": {"name": "BFILE"}, "targetType": {"name": "BFILE"}}, {"sourceType": {"name": "NCLOB"}, "targetType": {"name": "NCLOB"}}, {"sourceType": {"name": "ROWID"}, "targetType": {"name": "ROWID"}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "UROWID"}, "targetType": {"name": "UROWID"}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "URITYPE"}, "targetType": {"name": "URITYPE"}}, {"sourceType": {"name": "HTTPURITYPE"}, "targetType": {"name": "HTTPURITYPE"}}, {"sourceType": {"name": "INTERVALYM"}, "targetType": {"name": "INTERVALYM"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL(\\s*)YEAR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)MONTH", "INTERVAL(\\s*)YEAR(\\s*)TO(\\s*)MONTH"], "regularReplacements": ["INTERVAL YEAR(${PRECISION}) TO MONTH", "INTERVAL YEAR TO MONTH"]}, {"sourceType": {"name": "INTERVALDS"}, "targetType": {"name": "INTERVALDS"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND"], "regularReplacements": ["INTERVAL DAY(${PRECISION}) TO SECOND(${SCALE})", "INTERVAL DAY(${PRECISION}) TO SECOND", "INTERVAL DAY(${PRECISION}) TO SECOND(${SCALE})", "INTERVAL DAY(${PRECISION}) TO SECOND"]}, {"sourceType": {"name": "LONG RAW"}, "targetType": {"name": "LONG RAW"}}, {"sourceType": {"name": "RAW", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "RAW", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "NVARCHAR2", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "NVARCHAR2", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "DATE"}}]