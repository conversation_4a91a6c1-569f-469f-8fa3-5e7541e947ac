[{"sourceType": {"name": "interval year(4) to month"}, "targetType": {"name": "interval year to month"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL(\\s*)YEAR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)MONTH", "INTERVAL(\\s*)YEAR(\\s*)TO(\\s*)MONTH"], "regularReplacements": ["interval year to month", "interval year to month"]}, {"sourceType": {"name": "interval day(2) to second"}, "targetType": {"name": "interval day(2) to second"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND"], "regularReplacements": ["interval day to second(${SCALE})", "interval day to second", "interval day to second(${SCALE})", "interval day to second"]}, {"sourceType": {"name": "interval day(2) to fraction(5)"}, "targetType": {"name": "interval day to fraction(5)"}}, {"sourceType": {"name": "interval hour(2) to second"}, "targetType": {"name": "interval hour to second"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL(\\s*)HOUR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)HOUR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)HOUR(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)HOUR(\\s*)TO(\\s*)SECOND"], "regularReplacements": ["interval hour(${PRECISION}) to second(${SCALE})", "interval hour(${PRECISION}) to second", "interval hour(${PRECISION}) to second(${SCALE})", "interval hour(${PRECISION}) to second"]}, {"sourceType": {"name": "interval hour(2) to minute"}, "targetType": {"name": "interval hour to minute"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL(\\s*)HOUR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)MINUTE(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)HOUR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)MINUTE", "INTERVAL(\\s*)HOUR(\\s*)TO(\\s*)MINUTE(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)HOUR(\\s*)TO(\\s*)MINUTE"], "regularReplacements": ["interval hour(${PRECISION}) to minute(${SCALE})", "interval hour(${PRECISION}) to minute", "interval hour(${PRECISION}) to minute(${SCALE})", "interval hour(${PRECISION}) to minute"]}, {"sourceType": {"name": "byte"}, "targetType": {"name": "byte"}}]