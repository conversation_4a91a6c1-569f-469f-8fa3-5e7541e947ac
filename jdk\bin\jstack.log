2023-09-20 11:33:08
Full thread dump OpenJDK 64-Bit Server VM (11.0.2+9 mixed mode):

Threads class SMR info:
_java_thread_list=0x00000102f285fa80, length=132, elements={
0x00000102f01d4000, 0x00000102f01d6800, 0x00000102f01ff000, 0x00000102f0a9a000,
0x00000102f0a9b000, 0x00000102f0208000, 0x00000102f0aa3000, 0x00000102f0c3a800,
0x00000102f0c46800, 0x00000102f40de800, 0x00000102f1621000, 0x00000102f414f800,
0x00000102f44ab800, 0x00000102f3032000, 0x00000102f44cc800, 0x00000102f2eeb800,
0x00000102f61b4000, 0x00000102f3d99800, 0x00000102f6447000, 0x00000102f2693800,
0x00000102f2694800, 0x00000102f2695000, 0x00000102f2690800, 0x00000102f2696000,
0x00000102f2691000, 0x00000102f2697000, 0x00000102f2692000, 0x00000102f6278000,
0x00000102f627a800, 0x00000102f6278800, 0x00000102f6279800, 0x00000102f627b000,
0x00000102f627c000, 0x00000102f6276000, 0x00000102f627c800, 0x00000102f6277000,
0x00000102f627d800, 0x00000102f6281000, 0x00000102f6281800, 0x00000102f6282800,
0x00000102f6280000, 0x00000102f6283000, 0x00000102f627e800, 0x00000102f2692800,
0x00000102f1e29000, 0x00000102f1e27000, 0x00000102f2cee000, 0x00000102f2737000,
0x00000102f32ab000, 0x00000102f32a8800, 0x00000102f32a9800, 0x00000102f32b0000,
0x00000102f32ae800, 0x00000102f32af000, 0x00000102f2732000, 0x00000102f2739800,
0x00000102f2736000, 0x00000102f2733000, 0x00000102f45b2000, 0x00000102f45af800,
0x00000102f2738800, 0x00000102f3140000, 0x00000102f0fb5000, 0x00000102f3dbd000,
0x00000102f45a1000, 0x00000102f2740000, 0x00000102f273c000, 0x00000102f273f000,
0x00000102f273d800, 0x00000102f2741000, 0x00000102f45a2000, 0x00000102f3141000,
0x00000102f3dbc000, 0x00000102f3dbb000, 0x00000102f3dbd800, 0x00000102f420f800,
0x00000102f420e800, 0x00000102f4210800, 0x00000102f27e4800, 0x00000102f4211000,
0x00000102f2bab800, 0x00000102f2bac800, 0x00000102f313f800, 0x00000102f2ba6800,
0x00000102f2726800, 0x00000102f2ba8800, 0x00000102f2728000, 0x00000102f2baa000,
0x00000102f273c800, 0x00000102f273b000, 0x00000102f12b6000, 0x00000102f12b2800,
0x00000102f12b5000, 0x00000102f12b0000, 0x00000102f272a800, 0x00000102f2727000,
0x00000102f272c000, 0x00000102f272d000, 0x00000102f0fb6000, 0x00000102f2734800,
0x00000102f0fb7800, 0x00000102f12b7000, 0x00000102f12b3800, 0x00000102f332d800,
0x00000102f332b000, 0x00000102f332b800, 0x00000102f332f000, 0x00000102f12b1000,
0x00000102f7cc0800, 0x00000102f3141800, 0x00000102f279b000, 0x00000102f279b800,
0x00000102f2bab000, 0x00000102f27e5000, 0x00000102f2bad800, 0x00000102f27e0000,
0x00000102f332c800, 0x00000102f332e000, 0x00000102f7cbf800, 0x00000102f3329800,
0x00000102f7cc1800, 0x00000102f272b000, 0x00000102f3330000, 0x00000102f7cbf000,
0x00000102f0bca000, 0x00000102f27e6000, 0x00000102f1021000, 0x00000102f332a000,
0x00000102f3330800, 0x00000102f1022800, 0x00000102f279d800, 0x00000102f2ba9000
}

"Reference Handler" #2 daemon prio=10 os_prio=2 cpu=15.63ms elapsed=145118.91s tid=0x00000102f01d4000 nid=0xfa6c waiting on condition  [0x0000000159eff000]
   java.lang.Thread.State: RUNNABLE
	at java.lang.ref.Reference.waitForReferencePendingList(java.base@11.0.2/Native Method)
	at java.lang.ref.Reference.processPendingReferences(java.base@11.0.2/Reference.java:241)
	at java.lang.ref.Reference$ReferenceHandler.run(java.base@11.0.2/Reference.java:213)

"Finalizer" #3 daemon prio=8 os_prio=1 cpu=0.00ms elapsed=145118.91s tid=0x00000102f01d6800 nid=0x11e08 in Object.wait()  [0x0000000159fff000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.ref.ReferenceQueue.remove(java.base@11.0.2/ReferenceQueue.java:155)
	- waiting to re-lock in wait() <0x00000007000003e0> (a java.lang.ref.ReferenceQueue$Lock)
	at java.lang.ref.ReferenceQueue.remove(java.base@11.0.2/ReferenceQueue.java:176)
	at java.lang.ref.Finalizer$FinalizerThread.run(java.base@11.0.2/Finalizer.java:170)

"Signal Dispatcher" #4 daemon prio=9 os_prio=2 cpu=0.00ms elapsed=145118.89s tid=0x00000102f01ff000 nid=0x2474 runnable  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"Attach Listener" #5 daemon prio=5 os_prio=2 cpu=0.00ms elapsed=145118.89s tid=0x00000102f0a9a000 nid=0x3db8 waiting on condition  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"C2 CompilerThread0" #6 daemon prio=9 os_prio=2 cpu=74687.50ms elapsed=145118.89s tid=0x00000102f0a9b000 nid=0x11e94 waiting on condition  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE
   No compile task

"C1 CompilerThread0" #8 daemon prio=9 os_prio=2 cpu=7843.75ms elapsed=145118.89s tid=0x00000102f0208000 nid=0xe0f8 waiting on condition  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE
   No compile task

"Sweeper thread" #9 daemon prio=9 os_prio=2 cpu=3937.50ms elapsed=145118.89s tid=0x00000102f0aa3000 nid=0x12a6c runnable  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"Common-Cleaner" #10 daemon prio=8 os_prio=1 cpu=0.00ms elapsed=145118.85s tid=0x00000102f0c3a800 nid=0x11ccc in Object.wait()  [0x000000015a5fe000]
   java.lang.Thread.State: TIMED_WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.ref.ReferenceQueue.remove(java.base@11.0.2/ReferenceQueue.java:155)
	- waiting to re-lock in wait() <0x0000000700000ff8> (a java.lang.ref.ReferenceQueue$Lock)
	at jdk.internal.ref.CleanerImpl.run(java.base@11.0.2/CleanerImpl.java:148)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)
	at jdk.internal.misc.InnocuousThread.run(java.base@11.0.2/InnocuousThread.java:134)

"Service Thread" #11 daemon prio=9 os_prio=0 cpu=0.00ms elapsed=145118.84s tid=0x00000102f0c46800 nid=0xf370 runnable  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"Druid-ConnectionPool-Create-458998685" #16 daemon prio=5 os_prio=0 cpu=359.38ms elapsed=145114.58s tid=0x00000102f40de800 nid=0x11998 waiting on condition  [0x000000015a9fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x000000070089f358> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2813)

"Druid-ConnectionPool-Destroy-458998685" #17 daemon prio=5 os_prio=0 cpu=15.63ms elapsed=145114.58s tid=0x00000102f1621000 nid=0x754c waiting on condition  [0x000000015b0ff000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@11.0.2/Native Method)
	at com.alibaba.druid.pool.DruidDataSource$DestroyConnectionThread.run(DruidDataSource.java:2913)

"MVStore background writer C:/Users/<USER>/Desktop/KDTS/kdts-plus-bs-20230913/conf/h2/dts.mv.db" #18 daemon prio=5 os_prio=0 cpu=21531.25ms elapsed=145114.18s tid=0x00000102f414f800 nid=0xf59c in Object.wait()  [0x000000015b1fe000]
   java.lang.Thread.State: TIMED_WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at org.h2.mvstore.FileStore$BackgroundWriterThread.run(FileStore.java:2243)
	- waiting to re-lock in wait() <0x00000007008d0710> (a java.lang.Object)

"Catalina-utility-1" #19 prio=1 os_prio=-2 cpu=2562.50ms elapsed=145114.08s tid=0x00000102f44ab800 nid=0xfbf8 waiting on condition  [0x000000015b2ff000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007008869e8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"Catalina-utility-2" #20 prio=1 os_prio=-2 cpu=1546.88ms elapsed=145114.08s tid=0x00000102f3032000 nid=0xf3d8 waiting on condition  [0x000000015b3fe000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007008869e8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"container-0" #21 prio=5 os_prio=0 cpu=78.13ms elapsed=145114.08s tid=0x00000102f44cc800 nid=0x1046c waiting on condition  [0x000000015b4fe000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@11.0.2/Native Method)
	at org.apache.catalina.core.StandardServer.await(StandardServer.java:566)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer$1.run(TomcatWebServer.java:197)

"H2-serialization" #22 daemon prio=5 os_prio=0 cpu=46.88ms elapsed=145113.82s tid=0x00000102f2eeb800 nid=0x4ad0 waiting on condition  [0x000000015b5fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x000000070089f578> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"H2-save" #23 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=145113.81s tid=0x00000102f61b4000 nid=0x7030 waiting on condition  [0x000000015b6fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007008d0c08> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"https-jsse-nio-54524-exec-1" #24 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=145112.47s tid=0x00000102f3d99800 nid=0x11168 waiting on condition  [0x000000015b7fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000702729b30> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"https-jsse-nio-54524-exec-2" #25 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=145112.47s tid=0x00000102f6447000 nid=0x539c waiting on condition  [0x000000015b8ff000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000702729b30> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"https-jsse-nio-54524-exec-3" #26 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=145112.47s tid=0x00000102f2693800 nid=0xf8a0 waiting on condition  [0x000000015b9fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000702729b30> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"https-jsse-nio-54524-exec-4" #27 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=145112.47s tid=0x00000102f2694800 nid=0x12708 waiting on condition  [0x000000015bafe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000702729b30> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"https-jsse-nio-54524-exec-5" #28 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=145112.47s tid=0x00000102f2695000 nid=0xf7a8 waiting on condition  [0x000000015bbfe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000702729b30> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"https-jsse-nio-54524-exec-6" #29 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=145112.47s tid=0x00000102f2690800 nid=0xf98c waiting on condition  [0x000000015bcfe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000702729b30> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"https-jsse-nio-54524-exec-7" #30 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=145112.47s tid=0x00000102f2696000 nid=0x1c8c waiting on condition  [0x000000015bdfe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000702729b30> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"https-jsse-nio-54524-exec-8" #31 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=145112.47s tid=0x00000102f2691000 nid=0x12b7c waiting on condition  [0x000000015beff000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000702729b30> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"https-jsse-nio-54524-exec-9" #32 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=145112.46s tid=0x00000102f2697000 nid=0xcff8 waiting on condition  [0x000000015bffe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000702729b30> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"https-jsse-nio-54524-exec-10" #33 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=145112.46s tid=0x00000102f2692000 nid=0x11b64 waiting on condition  [0x000000015c0fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000702729b30> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"https-jsse-nio-54524-Poller" #34 daemon prio=5 os_prio=0 cpu=265.63ms elapsed=145112.45s tid=0x00000102f6278000 nid=0x12a28 runnable  [0x000000015c1fe000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(java.base@11.0.2/Native Method)
	at sun.nio.ch.WindowsSelectorImpl$SubSelector.poll(java.base@11.0.2/WindowsSelectorImpl.java:339)
	at sun.nio.ch.WindowsSelectorImpl.doSelect(java.base@11.0.2/WindowsSelectorImpl.java:167)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@11.0.2/SelectorImpl.java:124)
	- locked <0x000000070272e598> (a sun.nio.ch.Util$2)
	- locked <0x000000070272e510> (a sun.nio.ch.WindowsSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@11.0.2/SelectorImpl.java:136)
	at org.apache.tomcat.util.net.NioEndpoint$Poller.run(NioEndpoint.java:805)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"https-jsse-nio-54524-Acceptor" #35 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=145112.45s tid=0x00000102f627a800 nid=0x12a54 runnable  [0x000000015c2fe000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.ServerSocketChannelImpl.accept0(java.base@11.0.2/Native Method)
	at sun.nio.ch.ServerSocketChannelImpl.accept(java.base@11.0.2/ServerSocketChannelImpl.java:533)
	at sun.nio.ch.ServerSocketChannelImpl.accept(java.base@11.0.2/ServerSocketChannelImpl.java:285)
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:547)
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:79)
	at org.apache.tomcat.util.net.Acceptor.run(Acceptor.java:129)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"http-nio-54523-exec-1" #36 daemon prio=5 os_prio=0 cpu=10281.25ms elapsed=145112.45s tid=0x00000102f6278800 nid=0x45ac waiting on condition  [0x000000015c3ff000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x000000070272eaf8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"http-nio-54523-exec-2" #37 daemon prio=5 os_prio=0 cpu=10531.25ms elapsed=145112.45s tid=0x00000102f6279800 nid=0x12b18 waiting on condition  [0x000000015c4ff000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x000000070272eaf8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"http-nio-54523-exec-3" #38 daemon prio=5 os_prio=0 cpu=11812.50ms elapsed=145112.45s tid=0x00000102f627b000 nid=0xa03c waiting on condition  [0x000000015c5ff000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x000000070272eaf8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"http-nio-54523-exec-4" #39 daemon prio=5 os_prio=0 cpu=10312.50ms elapsed=145112.45s tid=0x00000102f627c000 nid=0x12a40 waiting on condition  [0x000000015c6fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x000000070272eaf8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"http-nio-54523-exec-5" #40 daemon prio=5 os_prio=0 cpu=10265.63ms elapsed=145112.45s tid=0x00000102f6276000 nid=0x11c58 waiting on condition  [0x000000015c7ff000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x000000070272eaf8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"http-nio-54523-exec-6" #41 daemon prio=5 os_prio=0 cpu=9812.50ms elapsed=145112.45s tid=0x00000102f627c800 nid=0xdefc waiting on condition  [0x000000015c8ff000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x000000070272eaf8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"http-nio-54523-exec-7" #42 daemon prio=5 os_prio=0 cpu=9750.00ms elapsed=145112.45s tid=0x00000102f6277000 nid=0x11910 waiting on condition  [0x000000015c9fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x000000070272eaf8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"http-nio-54523-exec-8" #43 daemon prio=5 os_prio=0 cpu=10046.88ms elapsed=145112.45s tid=0x00000102f627d800 nid=0x85ec waiting on condition  [0x000000015cafe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x000000070272eaf8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"http-nio-54523-exec-9" #44 daemon prio=5 os_prio=0 cpu=9765.63ms elapsed=145112.45s tid=0x00000102f6281000 nid=0x12650 waiting on condition  [0x000000015cbfe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x000000070272eaf8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"http-nio-54523-exec-10" #45 daemon prio=5 os_prio=0 cpu=9484.38ms elapsed=145112.45s tid=0x00000102f6281800 nid=0x120c8 waiting on condition  [0x000000015ccfe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x000000070272eaf8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@11.0.2/LinkedBlockingQueue.java:433)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"http-nio-54523-Poller" #46 daemon prio=5 os_prio=0 cpu=1953.13ms elapsed=145112.44s tid=0x00000102f6282800 nid=0x67f4 runnable  [0x000000015cdff000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(java.base@11.0.2/Native Method)
	at sun.nio.ch.WindowsSelectorImpl$SubSelector.poll(java.base@11.0.2/WindowsSelectorImpl.java:339)
	at sun.nio.ch.WindowsSelectorImpl.doSelect(java.base@11.0.2/WindowsSelectorImpl.java:167)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@11.0.2/SelectorImpl.java:124)
	- locked <0x0000000702a6bdb8> (a sun.nio.ch.Util$2)
	- locked <0x0000000702a2c8e0> (a sun.nio.ch.WindowsSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@11.0.2/SelectorImpl.java:136)
	at org.apache.tomcat.util.net.NioEndpoint$Poller.run(NioEndpoint.java:805)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"http-nio-54523-Acceptor" #47 daemon prio=5 os_prio=0 cpu=109.38ms elapsed=145112.44s tid=0x00000102f6280000 nid=0x2114 runnable  [0x000000015ceff000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.ServerSocketChannelImpl.accept0(java.base@11.0.2/Native Method)
	at sun.nio.ch.ServerSocketChannelImpl.accept(java.base@11.0.2/ServerSocketChannelImpl.java:533)
	at sun.nio.ch.ServerSocketChannelImpl.accept(java.base@11.0.2/ServerSocketChannelImpl.java:285)
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:547)
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:79)
	at org.apache.tomcat.util.net.Acceptor.run(Acceptor.java:129)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"DestroyJavaVM" #48 prio=5 os_prio=0 cpu=4890.63ms elapsed=145112.25s tid=0x00000102f6283000 nid=0xe7f0 waiting on condition  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"mysql-cj-abandoned-connection-cleanup" #49 daemon prio=5 os_prio=0 cpu=390.63ms elapsed=145040.65s tid=0x00000102f627e800 nid=0x12670 in Object.wait()  [0x00000001594fe000]
   java.lang.Thread.State: TIMED_WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.ref.ReferenceQueue.remove(java.base@11.0.2/ReferenceQueue.java:155)
	- waiting to re-lock in wait() <0x0000000702ae10a0> (a java.lang.ref.ReferenceQueue$Lock)
	at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"Abandoned connection cleanup thread" #50 daemon prio=5 os_prio=0 cpu=484.38ms elapsed=145040.64s tid=0x00000102f2692800 nid=0x11b9c in Object.wait()  [0x00000001595fe000]
   java.lang.Thread.State: TIMED_WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.ref.ReferenceQueue.remove(java.base@11.0.2/ReferenceQueue.java:155)
	- waiting to re-lock in wait() <0x0000000702ac4e88> (a java.lang.ref.ReferenceQueue$Lock)
	at com.mysql.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:64)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"mysql housekeeper" #63 daemon prio=5 os_prio=0 cpu=78.13ms elapsed=144805.30s tid=0x00000102f1e29000 nid=0xf590 waiting on condition  [0x000000015d4fe000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007027dbd00> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"kingbase-mysql housekeeper" #66 daemon prio=5 os_prio=0 cpu=93.75ms elapsed=144804.93s tid=0x00000102f1e27000 nid=0xd69c waiting on condition  [0x000000015d6fe000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000702926c78> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"mysql housekeeper" #143 daemon prio=5 os_prio=0 cpu=62.50ms elapsed=144376.91s tid=0x00000102f2cee000 nid=0x10670 waiting on condition  [0x000000015d0ff000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047eb7f8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"kingbase-mysql housekeeper" #145 daemon prio=5 os_prio=0 cpu=93.75ms elapsed=144376.58s tid=0x00000102f2737000 nid=0x12b28 waiting on condition  [0x000000015d2ff000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047ec008> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"logback-1" #210 daemon prio=5 os_prio=0 cpu=46.88ms elapsed=91022.40s tid=0x00000102f32ab000 nid=0xc88c waiting on condition  [0x00000001596fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047eafd0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1170)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"logback-2" #211 daemon prio=5 os_prio=0 cpu=31.25ms elapsed=91022.40s tid=0x00000102f32a8800 nid=0x10048 waiting on condition  [0x000000015a8fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047eafd0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1170)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"logback-3" #215 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=89660.79s tid=0x00000102f32a9800 nid=0x4ee4 waiting on condition  [0x000000015d1fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047eafd0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1170)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"mysql housekeeper" #216 daemon prio=5 os_prio=0 cpu=46.88ms elapsed=86886.41s tid=0x00000102f32b0000 nid=0x3768 waiting on condition  [0x000000015d5fe000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047eb408> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"kingbase-mysql housekeeper" #219 daemon prio=5 os_prio=0 cpu=93.75ms elapsed=86886.02s tid=0x00000102f32ae800 nid=0x3744 waiting on condition  [0x000000015d9fe000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047ec818> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"logback-4" #222 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=86885.44s tid=0x00000102f32af000 nid=0xb56c waiting on condition  [0x000000015dbfe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047eafd0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1170)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"mysql housekeeper" #322 daemon prio=5 os_prio=0 cpu=46.88ms elapsed=85328.72s tid=0x00000102f2732000 nid=0x10aec waiting on condition  [0x000000015d7ff000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047ebc00> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"kingbase-mysql housekeeper" #325 daemon prio=5 os_prio=0 cpu=31.25ms elapsed=85328.37s tid=0x00000102f2739800 nid=0x12010 waiting on condition  [0x000000015deff000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047ea9c0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"mysql housekeeper" #340 daemon prio=5 os_prio=0 cpu=46.88ms elapsed=79685.27s tid=0x00000102f2736000 nid=0x11180 waiting on condition  [0x000000015d8fe000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047eca58> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"kingbase-mysql housekeeper" #343 daemon prio=5 os_prio=0 cpu=93.75ms elapsed=79684.90s tid=0x00000102f2733000 nid=0x30c8 waiting on condition  [0x000000015ddff000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047ece80> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"mysql housekeeper" #1355 daemon prio=5 os_prio=0 cpu=46.88ms elapsed=61414.08s tid=0x00000102f45b2000 nid=0xba3c waiting on condition  [0x000000015dafe000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007089f8e18> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"kingbase-mysql housekeeper" #1358 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=61413.73s tid=0x00000102f45af800 nid=0x10184 waiting on condition  [0x000000015e0ff000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000708a00ac8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"mysql housekeeper" #1470 daemon prio=5 os_prio=0 cpu=46.88ms elapsed=42812.15s tid=0x00000102f2738800 nid=0xbdb8 waiting on condition  [0x000000015dcfe000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007091a2138> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"kingbase-mysql housekeeper" #1473 daemon prio=5 os_prio=0 cpu=78.13ms elapsed=42811.75s tid=0x00000102f3140000 nid=0xb06c waiting on condition  [0x000000015e4ff000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007091967e8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"Kingbase8-JDBC-SharedTimer-12" #1475 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=42811.53s tid=0x00000102f0fb5000 nid=0x12b44 in Object.wait()  [0x000000015e5fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at java.util.TimerThread.mainLoop(java.base@11.0.2/Timer.java:527)
	- waiting to re-lock in wait() <0x0000000709196a00> (a java.util.TaskQueue)
	at java.util.TimerThread.run(java.base@11.0.2/Timer.java:506)

"ForkJoinPool.commonPool-worker-3" #1481 daemon prio=5 os_prio=0 cpu=906.25ms elapsed=42810.82s tid=0x00000102f3dbd000 nid=0xf01c waiting on condition  [0x000000015ecff000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000702ae1708> (a java.util.concurrent.ForkJoinPool)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.ForkJoinPool.runWorker(java.base@11.0.2/ForkJoinPool.java:1628)
	at java.util.concurrent.ForkJoinWorkerThread.run(java.base@11.0.2/ForkJoinWorkerThread.java:177)

"read-31" #1484 prio=5 os_prio=0 cpu=375.00ms elapsed=42809.36s tid=0x00000102f45a1000 nid=0xbb50 waiting on condition  [0x000000015effe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000701acbaf0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.put(java.base@11.0.2/LinkedBlockingQueue.java:341)
	at com.kingbase.kdts.thread.ThreadPoolWorkQueue.offer(ThreadPoolWorkQueue.java:24)
	at java.util.concurrent.ThreadPoolExecutor.execute(java.base@11.0.2/ThreadPoolExecutor.java:1347)
	at java.util.concurrent.AbstractExecutorService.submit(java.base@11.0.2/AbstractExecutorService.java:140)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:72)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:60)
	at com.kingbase.kdts.job.migration.MigrationJobManager.submitNewMigrationJob(MigrationJobManager.java:1306)
	at com.kingbase.kdts.job.migration.MigrationContext.submitNewMigrationJob(MigrationContext.java:1061)
	at com.kingbase.kdts.job.migration.b.a(AbstractReadJob.java:194)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:858)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:735)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:129)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:24)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"read-32" #1485 prio=5 os_prio=0 cpu=31.25ms elapsed=42809.15s tid=0x00000102f2740000 nid=0xdbfc waiting on condition  [0x000000015e6fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000701acbaf0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.put(java.base@11.0.2/LinkedBlockingQueue.java:341)
	at com.kingbase.kdts.thread.ThreadPoolWorkQueue.offer(ThreadPoolWorkQueue.java:24)
	at java.util.concurrent.ThreadPoolExecutor.execute(java.base@11.0.2/ThreadPoolExecutor.java:1347)
	at java.util.concurrent.AbstractExecutorService.submit(java.base@11.0.2/AbstractExecutorService.java:140)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:72)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:60)
	at com.kingbase.kdts.job.migration.MigrationJobManager.submitNewMigrationJob(MigrationJobManager.java:1306)
	at com.kingbase.kdts.job.migration.MigrationContext.submitNewMigrationJob(MigrationContext.java:1061)
	at com.kingbase.kdts.job.migration.b.a(AbstractReadJob.java:194)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:858)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:735)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:129)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:24)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"read-33" #1486 prio=5 os_prio=0 cpu=281.25ms elapsed=42809.15s tid=0x00000102f273c000 nid=0xf60 waiting on condition  [0x000000015f0fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000701acbaf0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.put(java.base@11.0.2/LinkedBlockingQueue.java:341)
	at com.kingbase.kdts.thread.ThreadPoolWorkQueue.offer(ThreadPoolWorkQueue.java:24)
	at java.util.concurrent.ThreadPoolExecutor.execute(java.base@11.0.2/ThreadPoolExecutor.java:1347)
	at java.util.concurrent.AbstractExecutorService.submit(java.base@11.0.2/AbstractExecutorService.java:140)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:72)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:60)
	at com.kingbase.kdts.job.migration.MigrationJobManager.submitNewMigrationJob(MigrationJobManager.java:1306)
	at com.kingbase.kdts.job.migration.MigrationContext.submitNewMigrationJob(MigrationContext.java:1061)
	at com.kingbase.kdts.job.migration.b.a(AbstractReadJob.java:194)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:858)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:735)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:129)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:24)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"read-34" #1487 prio=5 os_prio=0 cpu=187.50ms elapsed=42809.15s tid=0x00000102f273f000 nid=0xd5f8 waiting on condition  [0x000000015f1fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000701acbaf0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.put(java.base@11.0.2/LinkedBlockingQueue.java:341)
	at com.kingbase.kdts.thread.ThreadPoolWorkQueue.offer(ThreadPoolWorkQueue.java:24)
	at java.util.concurrent.ThreadPoolExecutor.execute(java.base@11.0.2/ThreadPoolExecutor.java:1347)
	at java.util.concurrent.AbstractExecutorService.submit(java.base@11.0.2/AbstractExecutorService.java:140)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:72)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:60)
	at com.kingbase.kdts.job.migration.MigrationJobManager.submitNewMigrationJob(MigrationJobManager.java:1306)
	at com.kingbase.kdts.job.migration.MigrationContext.submitNewMigrationJob(MigrationContext.java:1061)
	at com.kingbase.kdts.job.migration.b.a(AbstractReadJob.java:194)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:858)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:735)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:129)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:24)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"read-35" #1488 prio=5 os_prio=0 cpu=31.25ms elapsed=42809.14s tid=0x00000102f273d800 nid=0x102e0 waiting on condition  [0x000000015f2fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000701acbaf0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.put(java.base@11.0.2/LinkedBlockingQueue.java:341)
	at com.kingbase.kdts.thread.ThreadPoolWorkQueue.offer(ThreadPoolWorkQueue.java:24)
	at java.util.concurrent.ThreadPoolExecutor.execute(java.base@11.0.2/ThreadPoolExecutor.java:1347)
	at java.util.concurrent.AbstractExecutorService.submit(java.base@11.0.2/AbstractExecutorService.java:140)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:72)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:60)
	at com.kingbase.kdts.job.migration.MigrationJobManager.submitNewMigrationJob(MigrationJobManager.java:1306)
	at com.kingbase.kdts.job.migration.MigrationContext.submitNewMigrationJob(MigrationContext.java:1061)
	at com.kingbase.kdts.job.migration.b.a(AbstractReadJob.java:194)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:858)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:735)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:129)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:24)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"read-36" #1489 prio=5 os_prio=0 cpu=31.25ms elapsed=42809.14s tid=0x00000102f2741000 nid=0xf4f0 waiting on condition  [0x000000015f3fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000701acbaf0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.put(java.base@11.0.2/LinkedBlockingQueue.java:341)
	at com.kingbase.kdts.thread.ThreadPoolWorkQueue.offer(ThreadPoolWorkQueue.java:24)
	at java.util.concurrent.ThreadPoolExecutor.execute(java.base@11.0.2/ThreadPoolExecutor.java:1347)
	at java.util.concurrent.AbstractExecutorService.submit(java.base@11.0.2/AbstractExecutorService.java:140)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:72)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:60)
	at com.kingbase.kdts.job.migration.MigrationJobManager.submitNewMigrationJob(MigrationJobManager.java:1306)
	at com.kingbase.kdts.job.migration.MigrationContext.submitNewMigrationJob(MigrationContext.java:1061)
	at com.kingbase.kdts.job.migration.b.a(AbstractReadJob.java:194)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:858)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:735)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:129)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:24)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-98" #1490 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.47s tid=0x00000102f45a2000 nid=0xc9d0 in Object.wait()  [0x000000015f4fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000709205580> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-99" #1491 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.46s tid=0x00000102f3141000 nid=0x31cc in Object.wait()  [0x000000015f5fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007092408a0> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-100" #1492 prio=5 os_prio=0 cpu=15.63ms elapsed=42808.46s tid=0x00000102f3dbc000 nid=0x5728 in Object.wait()  [0x000000015f6fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000709402aa0> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-101" #1493 prio=5 os_prio=0 cpu=15.63ms elapsed=42808.45s tid=0x00000102f3dbb000 nid=0x74c4 in Object.wait()  [0x000000015f7ff000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007094510e0> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-102" #1494 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.44s tid=0x00000102f3dbd800 nid=0x1e00 in Object.wait()  [0x000000015f8fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000709a7da80> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-103" #1495 prio=5 os_prio=0 cpu=15.63ms elapsed=42808.43s tid=0x00000102f420f800 nid=0x1200c in Object.wait()  [0x000000015f9fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000709ad4af0> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-104" #1496 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.42s tid=0x00000102f420e800 nid=0x4d90 in Object.wait()  [0x000000015fafe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000709d1c350> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-105" #1497 prio=5 os_prio=0 cpu=15.63ms elapsed=42808.41s tid=0x00000102f4210800 nid=0x6c30 in Object.wait()  [0x000000015fbfe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000709e08350> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-106" #1498 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.41s tid=0x00000102f27e4800 nid=0x7610 in Object.wait()  [0x000000015fcfe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000709ece230> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-107" #1499 prio=5 os_prio=0 cpu=15.63ms elapsed=42808.40s tid=0x00000102f4211000 nid=0x8b34 in Object.wait()  [0x000000015fdfe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000709f1aac8> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-108" #1500 prio=5 os_prio=0 cpu=15.63ms elapsed=42808.39s tid=0x00000102f2bab800 nid=0x11ad4 in Object.wait()  [0x000000015fefe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000709f4a108> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-109" #1501 prio=5 os_prio=0 cpu=31.25ms elapsed=42808.39s tid=0x00000102f2bac800 nid=0x9f38 in Object.wait()  [0x000000015fffe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070a480f00> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-110" #1502 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.38s tid=0x00000102f313f800 nid=0x7e84 in Object.wait()  [0x00000001600fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070a603b00> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-111" #1503 prio=5 os_prio=0 cpu=15.63ms elapsed=42808.38s tid=0x00000102f2ba6800 nid=0x5250 in Object.wait()  [0x00000001601fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070a6fee50> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-112" #1504 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.37s tid=0x00000102f2726800 nid=0xe290 in Object.wait()  [0x00000001602fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070a7568c8> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-113" #1505 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.36s tid=0x00000102f2ba8800 nid=0x440 in Object.wait()  [0x00000001603fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070a76b068> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-114" #1506 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.36s tid=0x00000102f2728000 nid=0x11f18 in Object.wait()  [0x00000001604fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070a7df958> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-115" #1507 prio=5 os_prio=0 cpu=15.63ms elapsed=42808.33s tid=0x00000102f2baa000 nid=0x11204 in Object.wait()  [0x00000001605fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070acbc4e8> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-116" #1508 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.29s tid=0x00000102f273c800 nid=0x5434 in Object.wait()  [0x00000001606fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070ad0c1b0> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-117" #1509 prio=5 os_prio=0 cpu=15.63ms elapsed=42808.25s tid=0x00000102f273b000 nid=0x10214 in Object.wait()  [0x00000001607fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070b034680> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-118" #1510 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.21s tid=0x00000102f12b6000 nid=0x65b0 in Object.wait()  [0x00000001608fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070b19da40> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-119" #1511 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.17s tid=0x00000102f12b2800 nid=0xadcc in Object.wait()  [0x0000000160afe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070b1f3088> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-120" #1512 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.13s tid=0x00000102f12b5000 nid=0xb018 in Object.wait()  [0x0000000160bfe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070b6a34e0> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"write-121" #1513 prio=5 os_prio=0 cpu=0.00ms elapsed=42808.08s tid=0x00000102f12b0000 nid=0x12140 in Object.wait()  [0x0000000160cfe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000070b6fbd88> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"logback-5" #1523 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=7613.25s tid=0x00000102f272a800 nid=0x114a4 waiting on condition  [0x000000015eafe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047eafd0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1170)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"logback-6" #1524 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=7613.23s tid=0x00000102f2727000 nid=0x12440 waiting on condition  [0x000000015ebff000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047eafd0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1170)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"mysql housekeeper" #1525 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=7612.95s tid=0x00000102f272c000 nid=0x4c1c waiting on condition  [0x000000015edfe000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000766803360> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"kingbase-mysql housekeeper" #1527 daemon prio=5 os_prio=0 cpu=15.63ms elapsed=7612.58s tid=0x00000102f272d000 nid=0x12e24 waiting on condition  [0x0000000160dff000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000766803c50> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@11.0.2/LockSupport.java:234)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@11.0.2/AbstractQueuedSynchronizer.java:2123)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"logback-7" #1529 daemon prio=5 os_prio=0 cpu=0.00ms elapsed=7611.45s tid=0x00000102f0fb6000 nid=0x5f34 waiting on condition  [0x0000000160ffe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007047eafd0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:1170)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@11.0.2/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@11.0.2/ThreadPoolExecutor.java:1054)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1114)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"KdtsDotter" #1531 daemon prio=5 os_prio=0 cpu=156.25ms elapsed=7611.36s tid=0x00000102f2734800 nid=0xcae0 waiting on condition  [0x00000001610ff000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@11.0.2/Native Method)
	at com.kingbase.kdts.job.migration.MigrationJobManager.lambda$startDotter$20(MigrationJobManager.java:777)
	at com.kingbase.kdts.job.migration.MigrationJobManager$$Lambda$1038/0x000000080099fc40.run(Unknown Source)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"KdtsJobWatcher" #1532 daemon prio=5 os_prio=0 cpu=359.38ms elapsed=7610.94s tid=0x00000102f0fb7800 nid=0x29ac waiting on condition  [0x00000001611fe000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@11.0.2/Native Method)
	at com.kingbase.kdts.job.migration.MigrationJobManager.lambda$startJobWatcher$18(MigrationJobManager.java:657)
	at com.kingbase.kdts.job.migration.MigrationJobManager$$Lambda$1076/0x0000000800a69040.run(Unknown Source)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"KdtsJobDispatcher" #1533 daemon prio=5 os_prio=0 cpu=15.63ms elapsed=7610.94s tid=0x00000102f12b7000 nid=0xe574 waiting on condition  [0x00000001612fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x0000000700901968> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at com.kingbase.kdts.job.migration.MigrationJobManager.lambda$startJobDispatcher$19(MigrationJobManager.java:705)
	at com.kingbase.kdts.job.migration.MigrationJobManager$$Lambda$1078/0x0000000800a6d840.run(Unknown Source)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"readZ2A-23" #1534 prio=5 os_prio=0 cpu=250.00ms elapsed=7610.67s tid=0x00000102f12b3800 nid=0x103e4 waiting on condition  [0x00000001613fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007028c26f0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.put(java.base@11.0.2/LinkedBlockingQueue.java:341)
	at com.kingbase.kdts.thread.ThreadPoolWorkQueue.offer(ThreadPoolWorkQueue.java:24)
	at java.util.concurrent.ThreadPoolExecutor.execute(java.base@11.0.2/ThreadPoolExecutor.java:1347)
	at java.util.concurrent.AbstractExecutorService.submit(java.base@11.0.2/AbstractExecutorService.java:140)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:72)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:60)
	at com.kingbase.kdts.job.migration.MigrationJobManager.submitNewMigrationJob(MigrationJobManager.java:1306)
	at com.kingbase.kdts.job.migration.MigrationContext.submitNewMigrationJob(MigrationContext.java:1061)
	at com.kingbase.kdts.job.migration.b.a(AbstractReadJob.java:194)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:858)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:735)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:129)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:24)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"readZ2A-24" #1535 prio=5 os_prio=0 cpu=265.63ms elapsed=7610.48s tid=0x00000102f332d800 nid=0x2b6c waiting on condition  [0x000000015e7fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007028c26f0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.put(java.base@11.0.2/LinkedBlockingQueue.java:341)
	at com.kingbase.kdts.thread.ThreadPoolWorkQueue.offer(ThreadPoolWorkQueue.java:24)
	at java.util.concurrent.ThreadPoolExecutor.execute(java.base@11.0.2/ThreadPoolExecutor.java:1347)
	at java.util.concurrent.AbstractExecutorService.submit(java.base@11.0.2/AbstractExecutorService.java:140)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:72)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:60)
	at com.kingbase.kdts.job.migration.MigrationJobManager.submitNewMigrationJob(MigrationJobManager.java:1306)
	at com.kingbase.kdts.job.migration.MigrationContext.submitNewMigrationJob(MigrationContext.java:1061)
	at com.kingbase.kdts.job.migration.b.a(AbstractReadJob.java:194)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:858)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:735)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:129)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:24)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"readZ2A-25" #1536 prio=5 os_prio=0 cpu=125.00ms elapsed=7610.48s tid=0x00000102f332b000 nid=0x12c4c waiting on condition  [0x00000001614fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007028c26f0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.put(java.base@11.0.2/LinkedBlockingQueue.java:341)
	at com.kingbase.kdts.thread.ThreadPoolWorkQueue.offer(ThreadPoolWorkQueue.java:24)
	at java.util.concurrent.ThreadPoolExecutor.execute(java.base@11.0.2/ThreadPoolExecutor.java:1347)
	at java.util.concurrent.AbstractExecutorService.submit(java.base@11.0.2/AbstractExecutorService.java:140)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:72)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:60)
	at com.kingbase.kdts.job.migration.MigrationJobManager.submitNewMigrationJob(MigrationJobManager.java:1306)
	at com.kingbase.kdts.job.migration.MigrationContext.submitNewMigrationJob(MigrationContext.java:1061)
	at com.kingbase.kdts.job.migration.b.a(AbstractReadJob.java:194)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:858)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:735)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:129)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:24)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"readZ2A-26" #1537 prio=5 os_prio=0 cpu=93.75ms elapsed=7610.48s tid=0x00000102f332b800 nid=0x12f24 waiting on condition  [0x00000001615fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007028c26f0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.put(java.base@11.0.2/LinkedBlockingQueue.java:341)
	at com.kingbase.kdts.thread.ThreadPoolWorkQueue.offer(ThreadPoolWorkQueue.java:24)
	at java.util.concurrent.ThreadPoolExecutor.execute(java.base@11.0.2/ThreadPoolExecutor.java:1347)
	at java.util.concurrent.AbstractExecutorService.submit(java.base@11.0.2/AbstractExecutorService.java:140)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:72)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:60)
	at com.kingbase.kdts.job.migration.MigrationJobManager.submitNewMigrationJob(MigrationJobManager.java:1306)
	at com.kingbase.kdts.job.migration.MigrationContext.submitNewMigrationJob(MigrationContext.java:1061)
	at com.kingbase.kdts.job.migration.b.a(AbstractReadJob.java:194)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:858)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:735)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:129)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:24)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"readZ2A-27" #1538 prio=5 os_prio=0 cpu=359.38ms elapsed=7610.48s tid=0x00000102f332f000 nid=0x3ebc waiting on condition  [0x00000001616fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007028c26f0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.put(java.base@11.0.2/LinkedBlockingQueue.java:341)
	at com.kingbase.kdts.thread.ThreadPoolWorkQueue.offer(ThreadPoolWorkQueue.java:24)
	at java.util.concurrent.ThreadPoolExecutor.execute(java.base@11.0.2/ThreadPoolExecutor.java:1347)
	at java.util.concurrent.AbstractExecutorService.submit(java.base@11.0.2/AbstractExecutorService.java:140)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:72)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:60)
	at com.kingbase.kdts.job.migration.MigrationJobManager.submitNewMigrationJob(MigrationJobManager.java:1306)
	at com.kingbase.kdts.job.migration.MigrationContext.submitNewMigrationJob(MigrationContext.java:1061)
	at com.kingbase.kdts.job.migration.b.a(AbstractReadJob.java:194)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:858)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:735)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:129)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:24)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"readZ2A-28" #1539 prio=5 os_prio=0 cpu=265.63ms elapsed=7610.48s tid=0x00000102f12b1000 nid=0xafc4 waiting on condition  [0x00000001617fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@11.0.2/Native Method)
	- parking to wait for  <0x00000007028c26f0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@11.0.2/LockSupport.java:194)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@11.0.2/AbstractQueuedSynchronizer.java:2081)
	at java.util.concurrent.LinkedBlockingQueue.put(java.base@11.0.2/LinkedBlockingQueue.java:341)
	at com.kingbase.kdts.thread.ThreadPoolWorkQueue.offer(ThreadPoolWorkQueue.java:24)
	at java.util.concurrent.ThreadPoolExecutor.execute(java.base@11.0.2/ThreadPoolExecutor.java:1347)
	at java.util.concurrent.AbstractExecutorService.submit(java.base@11.0.2/AbstractExecutorService.java:140)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:72)
	at com.kingbase.kdts.job.JobScheduler.submitJob(JobScheduler.java:60)
	at com.kingbase.kdts.job.migration.MigrationJobManager.submitNewMigrationJob(MigrationJobManager.java:1306)
	at com.kingbase.kdts.job.migration.MigrationContext.submitNewMigrationJob(MigrationContext.java:1061)
	at com.kingbase.kdts.job.migration.b.a(AbstractReadJob.java:194)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:858)
	at com.kingbase.kdts.job.migration.y.a(TableDataReadJob.java:735)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:129)
	at com.kingbase.kdts.job.migration.p.call(PartitionTableDataReadJob.java:24)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-77" #1540 prio=5 os_prio=0 cpu=0.00ms elapsed=7607.76s tid=0x00000102f7cc0800 nid=0x5cf8 in Object.wait()  [0x00000001618fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000766ac7fc8> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-78" #1541 prio=5 os_prio=0 cpu=15.63ms elapsed=7606.92s tid=0x00000102f3141800 nid=0xacc0 in Object.wait()  [0x00000001619fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000766ae0838> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-79" #1542 prio=5 os_prio=0 cpu=15.63ms elapsed=7606.72s tid=0x00000102f279b000 nid=0x7a88 in Object.wait()  [0x0000000161afe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000766b19e98> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-80" #1543 prio=5 os_prio=0 cpu=15.63ms elapsed=7606.71s tid=0x00000102f279b800 nid=0xd290 in Object.wait()  [0x0000000161bfe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000766aa4c20> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-81" #1544 prio=5 os_prio=0 cpu=0.00ms elapsed=7606.13s tid=0x00000102f2bab000 nid=0x100b4 in Object.wait()  [0x0000000161cfe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000766a64b48> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-82" #1545 prio=5 os_prio=0 cpu=0.00ms elapsed=7606.05s tid=0x00000102f27e5000 nid=0xd838 in Object.wait()  [0x0000000161dfe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x0000000766a2d958> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-83" #1546 prio=5 os_prio=0 cpu=15.63ms elapsed=7605.56s tid=0x00000102f2bad800 nid=0xec68 in Object.wait()  [0x0000000161efe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007669f56b8> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-84" #1547 prio=5 os_prio=0 cpu=0.00ms elapsed=7605.47s tid=0x00000102f27e0000 nid=0xae5c in Object.wait()  [0x0000000161ffe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007669c1dd8> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-85" #1548 prio=5 os_prio=0 cpu=0.00ms elapsed=7605.12s tid=0x00000102f332c800 nid=0x123c8 in Object.wait()  [0x000000015eefe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007fbff2818> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-86" #1549 prio=5 os_prio=0 cpu=31.25ms elapsed=7605.10s tid=0x00000102f332e000 nid=0x10a48 in Object.wait()  [0x00000001620fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007669829b8> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-87" #1550 prio=5 os_prio=0 cpu=0.00ms elapsed=7604.94s tid=0x00000102f7cbf800 nid=0x11f58 in Object.wait()  [0x00000001621fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000076694a5c0> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-88" #1551 prio=5 os_prio=0 cpu=0.00ms elapsed=7604.85s tid=0x00000102f3329800 nid=0xb3a0 in Object.wait()  [0x00000001622fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x000000076691d770> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-89" #1552 prio=5 os_prio=0 cpu=0.00ms elapsed=7604.74s tid=0x00000102f7cc1800 nid=0xc714 in Object.wait()  [0x00000001623fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007668ed258> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-90" #1553 prio=5 os_prio=0 cpu=0.00ms elapsed=7604.36s tid=0x00000102f272b000 nid=0x59fc in Object.wait()  [0x00000001624fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007feacf4a0> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-91" #1554 prio=5 os_prio=0 cpu=0.00ms elapsed=7604.26s tid=0x00000102f3330000 nid=0xc5f0 in Object.wait()  [0x00000001625fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007fce07c88> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-92" #1555 prio=5 os_prio=0 cpu=15.63ms elapsed=7604.16s tid=0x00000102f7cbf000 nid=0xe244 in Object.wait()  [0x00000001626fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007fcef0880> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-93" #1556 prio=5 os_prio=0 cpu=0.00ms elapsed=7603.79s tid=0x00000102f0bca000 nid=0x12034 in Object.wait()  [0x00000001627fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007fc4533c8> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-94" #1557 prio=5 os_prio=0 cpu=0.00ms elapsed=7603.68s tid=0x00000102f27e6000 nid=0x3780 in Object.wait()  [0x00000001628fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007fc7cfc98> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-95" #1558 prio=5 os_prio=0 cpu=0.00ms elapsed=7603.58s tid=0x00000102f1021000 nid=0xf264 in Object.wait()  [0x00000001629fe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007fb6a89f8> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-96" #1559 prio=5 os_prio=0 cpu=0.00ms elapsed=7603.51s tid=0x00000102f332a000 nid=0x3b28 in Object.wait()  [0x0000000162aff000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007f9f6ecf0> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-97" #1560 prio=5 os_prio=0 cpu=0.00ms elapsed=7603.50s tid=0x00000102f3330800 nid=0x4824 in Object.wait()  [0x0000000162bfe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007fae4eab8> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-98" #1561 prio=5 os_prio=0 cpu=31.25ms elapsed=7603.12s tid=0x00000102f1022800 nid=0x86b4 in Object.wait()  [0x0000000162cfe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007fa9882e0> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-99" #1562 prio=5 os_prio=0 cpu=15.63ms elapsed=7603.05s tid=0x00000102f279d800 nid=0x12d1c in Object.wait()  [0x0000000162dfe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007fa08dce0> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"writeZ2A-100" #1563 prio=5 os_prio=0 cpu=0.00ms elapsed=7602.96s tid=0x00000102f2ba9000 nid=0x12928 in Object.wait()  [0x0000000162efe000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@11.0.2/Native Method)
	- waiting on <no object reference available>
	at java.lang.Object.wait(java.base@11.0.2/Object.java:328)
	at com.kingbase8.core.v3.QueryExecutorImpl.waitOnLock_(QueryExecutorImpl.java:222)
	at com.kingbase8.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:358)
	- waiting to re-lock in wait() <0x00000007f9aa8358> (a com.kingbase8.core.v3.QueryExecutorImpl)
	at com.kingbase8.jdbc.KbConnection.executeTransactionCommand(KbConnection.java:1369)
	at com.kingbase8.jdbc.KbConnection.commit(KbConnection.java:1422)
	at com.kingbase8.jdbc.KbConnection.setAutoCommit(KbConnection.java:1315)
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at com.kingbase.kdts.f.e.b.d(DbWriterCopyKingbase.java:108)
	at com.kingbase.kdts.f.a.a(AbstractDbWriter.java:171)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:264)
	at com.kingbase.kdts.job.migration.z.call(TableDataWriteJob.java:32)
	at java.util.concurrent.FutureTask.run(java.base@11.0.2/FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@11.0.2/ThreadPoolExecutor.java:1128)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@11.0.2/ThreadPoolExecutor.java:628)
	at java.lang.Thread.run(java.base@11.0.2/Thread.java:834)

"VM Thread" os_prio=2 cpu=1546.88ms elapsed=145118.92s tid=0x00000102f01cc000 nid=0x2c5c runnable  

"GC Thread#0" os_prio=2 cpu=5921.88ms elapsed=145118.95s tid=0x00000102cbf31000 nid=0x10f9c runnable  

"GC Thread#1" os_prio=2 cpu=5968.75ms elapsed=145117.58s tid=0x00000102f2e00000 nid=0x1208c runnable  

"GC Thread#2" os_prio=2 cpu=6812.50ms elapsed=145117.58s tid=0x00000102f1fb5000 nid=0x12a90 runnable  

"GC Thread#3" os_prio=2 cpu=6859.38ms elapsed=145117.58s tid=0x00000102f0f37800 nid=0xf618 runnable  

"GC Thread#4" os_prio=2 cpu=6281.25ms elapsed=145117.58s tid=0x00000102f1663000 nid=0x1164 runnable  

"GC Thread#5" os_prio=2 cpu=6515.63ms elapsed=145117.58s tid=0x00000102f1a26000 nid=0x12428 runnable  

"G1 Main Marker" os_prio=2 cpu=0.00ms elapsed=145118.95s tid=0x00000102cbf5f000 nid=0x127d4 runnable  

"G1 Conc#0" os_prio=2 cpu=62.50ms elapsed=145118.95s tid=0x00000102cbf60800 nid=0x1204c runnable  

"G1 Conc#1" os_prio=2 cpu=46.88ms elapsed=145117.57s tid=0x00000102f0cf4800 nid=0x128b8 runnable  

"G1 Refine#0" os_prio=2 cpu=62.50ms elapsed=145118.93s tid=0x00000102f00d8800 nid=0x11260 runnable  

"G1 Refine#1" os_prio=2 cpu=15.63ms elapsed=144809.47s tid=0x00000102f4465000 nid=0x47f8 runnable  

"G1 Refine#2" os_prio=2 cpu=0.00ms elapsed=144796.19s tid=0x00000102f4462000 nid=0x5758 runnable  

"G1 Young RemSet Sampling" os_prio=2 cpu=1296.88ms elapsed=145118.93s tid=0x00000102f00d9000 nid=0xbed8 runnable  
"VM Periodic Task Thread" os_prio=2 cpu=1906.25ms elapsed=145118.85s tid=0x00000102f0c48000 nid=0x11ef8 waiting on condition  

JNI global refs: 21, weak refs: 45

