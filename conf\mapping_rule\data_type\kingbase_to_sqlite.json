[{"sourceType": {"name": "SMALLSERIAL", "autoIncrement": true}, "targetType": {"name": "BIGINT auto_increment"}}, {"sourceType": {"name": "SERIAL", "autoIncrement": true}, "targetType": {"name": "BIGINT auto_increment"}}, {"sourceType": {"name": "BIGSERIAL", "autoIncrement": true}, "targetType": {"name": "BIGINT auto_increment"}}, {"sourceType": {"name": "pg_catalog.interval"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(50)"}}, {"sourceType": {"name": "TIMETZ"}, "targetType": {"name": "TIME WITH TIME ZONE"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "bytea"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "binary"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "_text"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "_INT4"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "VARCHARBYTE", "lengthMin": 1, "lengthMax": 8188}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8188}}]