#!/bin/bash

# 项目名称和版本
KDTS="kdts-app-console"
KDTS_VERSION=2.0.2.34
KDTS_JAR="${KDTS}-${KDTS_VERSION}.jar"

# bin 目录
BIN_PATH=$(cd `dirname $0`; pwd)
# 进入bin目录
cd `dirname $0`
# 到上一级目录
cd ..

# 基目录
BASE_PATH=`pwd`

#JAVA 路径 （优先使用JAVA_PATH目录下的jdk）
JAVA_PATH=${BASE_PATH}"/jdk"
REQUIRED_JAVA_VERSION="11" #要求最低Java版本（Java 11）

function greater_than_or_equal_to() { test "$(echo "$@" | tr " " "\n" | sort -rV | head -n 1)" == "$1"; }
JAVA_CMD=""
JAVA_VERSION=""
if [ -f "${JAVA_PATH}/bin/java" ]; then
    eval JAVA_VERSION=$(${JAVA_PATH}/bin/java -version 2>&1 |awk 'NR==1{print $3}' |sed 's/\"//g')
    if greater_than_or_equal_to $JAVA_VERSION $REQUIRED_JAVA_VERSION; then
        JAVA_CMD=${JAVA_PATH}/bin/java
    fi
fi
if [ -z "${JAVA_CMD}" ] && [ -f "${JAVA_HOME}/bin/java" ]; then
    eval JDK_JAVA_VERSION=$(${JAVA_HOME}/bin/java -version 2>&1 |awk 'NR==1{print $3}' |sed 's/\"//g')
    if greater_than_or_equal_to $JDK_JAVA_VERSION $REQUIRED_JAVA_VERSION; then
		    JAVA_CMD=${JAVA_HOME}/bin/java
    fi
fi
if [ -z "${JAVA_CMD}" ] && [ -f "${JRE_HOME}/bin/java" ]; then
    eval JRE_JAVA_VERSION=$(${JRE_HOME}/bin/java -version 2>&1 |awk 'NR==1{print $3}' |sed 's/\"//g')
    if greater_than_or_equal_to $JRE_JAVA_VERSION $REQUIRED_JAVA_VERSION; then
		    JAVA_CMD=${JRE_HOME}/bin/java
    fi
fi

# 外部配置文件绝对目录,如果是目录需要/结尾，也可以直接指定文件
# 如果指定的是目录,spring则会读取目录中的所有配置文件
CONFIG_DIR=${BASE_PATH}"/conf"

NOW_STR=`date '+%Y-%m-%d_%H-%M-%S'`

# 日志路径
LOG_DIR=${BASE_PATH}"/logs"
LOG_FILE="${KDTS}_${NOW_STR}.log"
LOG_PATH="${LOG_DIR}/${LOG_FILE}"

# 项目启动日志输出绝对路径
LOG_STARTUP_PATH="${LOG_DIR}/startup_${NOW_STR}.log"

# 启动日志
KDTS_STARTUP_LOG="================================================ ${NOW_STR} ================================================\n"

# 创建日志目录
if [[ ! -d "${LOG_DIR}" ]]; then
  mkdir "${LOG_DIR}"
fi

# 创建项目运行日志
echo "" > ${LOG_PATH}

#根据可用内存大小（三分之二）计算分配给JAVA的内存
TOTAL_MEMORY=$(free -m | awk -F '[ :]+' 'NR==2{print $2}')
USED_MEMORY=$(free -m | awk -F '[ :]+' 'NR==2{print $3}')
AVAILABLE_MEMORY=$[TOTAL_MEMORY - USED_MEMORY]
FREE_MEMORY=$(free -m | awk -F '[ :]+' 'NR==2{print $4}')
# 从空闲内存和可用内存中取大（操作系统会将空闲内存用作缓存）
if [[ ${FREE_MEMORY} -ge AVAILABLE_MEMORY ]];then
    AVAILABLE_MEMORY=${FREE_MEMORY}
fi
if [[ ${AVAILABLE_MEMORY} -ge 36864 ]];then
    JAVA_MEMORY=24G
elif [[ ${AVAILABLE_MEMORY} -ge 24576 ]];then
    JAVA_MEMORY=16G
elif [[ ${AVAILABLE_MEMORY} -ge 12288 ]];then
    JAVA_MEMORY=8G
elif [[ ${AVAILABLE_MEMORY} -ge 6144 ]];then
    JAVA_MEMORY=4G
elif [[ ${AVAILABLE_MEMORY} -ge 3072 ]];then
    JAVA_MEMORY=2G
else
    JAVA_MEMORY=$[AVAILABLE_MEMORY / 2]
    JAVA_MEMORY="${JAVA_MEMORY}M"
fi
#如果需要手动设置，把下面的注释（#）去掉后设置即可
#JAVA_MEMORY=18G

#CPU架构
CPU_ARCH=`arch`

#=======================================================
# JVM 参数设置
#=======================================================
JAVA_OPT="
-server
-Dfile.encoding=UTF-8
-Djava.awt.headless=true
-Dcache_enable=true
-Dconfig.path=${CONFIG_DIR}
-Djavax.xml.parsers.SAXParserFactory=com.sun.org.apache.xerces.internal.jaxp.SAXParserFactoryImpl
-Djavax.xml.transform.TransformerFactory=com.sun.org.apache.xalan.internal.xsltc.trax.TransformerFactoryImpl
-Djavax.xml.parsers.DocumentBuilderFactory=com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
-Djava.security.properties=./conf/enableLegacyTLS.security
-Duser.country=CN -Duser.language=zh
-Djsse.enableCBCProtection=false
--add-opens java.base/jdk.internal.loader=ALL-UNNAMED --add-opens jdk.zipfs/jdk.nio.zipfs=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/java.lang.invoke=ALL-UNNAMED
-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./logs/
-Xms${JAVA_MEMORY}
-Xmx${JAVA_MEMORY}
"

#-Xmn2g -XX:MaxDirectMemorySize=512m -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=512m -Xss1024k"

#申威平台不支持下面的优化参数
if [[ $CPU_ARCH != "sw_64" ]]; then
	JAVA_OPT="${JAVA_OPT} -XX:+UseG1GC -XX:G1ReservePercent=20 -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:G1HeapRegionSize=8M -XX:+SegmentedCodeCache -XX:+PrintCommandLineFlags -XX:+ExplicitGCInvokesConcurrent"
fi

#===========================================================
# 追加命令启动相关参数到日志文件中
#===========================================================

# 输出项目名称
KDTS_STARTUP_LOG="${KDTS_STARTUP_LOG}${KDTS} v${KDTS_VERSION}\n"
# 输出项目版本
KDTS_STARTUP_LOG="${KDTS_STARTUP_LOG}jar name: ${KDTS_JAR}\n"
# 输出项目根目录
KDTS_STARTUP_LOG="${KDTS_STARTUP_LOG}base path: ${BASE_PATH}\n"
# 输出项目bin路径
KDTS_STARTUP_LOG="${KDTS_STARTUP_LOG}bin path: ${BIN_PATH}\n"
# 输出项目config路径
KDTS_STARTUP_LOG="${KDTS_STARTUP_LOG}config path: ${CONFIG_DIR}\n"
# 打印日志路径
KDTS_STARTUP_LOG="${KDTS_STARTUP_LOG}log path: ${LOG_PATH}\n"
# 打印JAVA_PATH
KDTS_STARTUP_LOG="${KDTS_STARTUP_LOG}java path: ${JAVA_PATH}\n"
# 打印JVM配置
KDTS_STARTUP_LOG="${KDTS_STARTUP_LOG}JAVA_OPT: ${JAVA_OPT}\n"
#打印内存配置
KDTS_STARTUP_LOG="${KDTS_STARTUP_LOG}FreeMemory=${FREE_MEMORY}M JavaMemory=${JAVA_MEMORY}\n"
#打印CPU架构
KDTS_STARTUP_LOG="${KDTS_STARTUP_LOG}cpu_arch=${CPU_ARCH}\n"
# 打印启动命令
KDTS_STARTUP_LOG="${KDTS_STARTUP_LOG}startup command: ${JAVA_PATH}/bin/java ${JAVA_OPT} -jar ${BASE_PATH}/lib/${KDTS_JAR} > ${LOG_PATH} 2>&1 & \n"

#======================================================================
# 执行启动命令：后台启动项目,并将日志输出到项目根目录下的logs文件夹下
#======================================================================
if [ -z "${JAVA_CMD}" ]; then
    echo ${KDTS} ${KDTS_VERSION}
    echo "JAVA_PATH=${JAVA_PATH}"
    if [ ! -z "${JAVA_VERSION}" ]; then
        echo "Error: The java version (${JAVA_VERSION}) is too old. It must have at least '${REQUIRED_JAVA_VERSION}' installed."
    else
        echo "Error: The '${JAVA_PATH}/bin/java' does not exist, it is needed to run this program. Please check the 'JAVA_PATH' in 'startup.sh'."
    fi
    exit 1
fi

echo ------------------------------------------------------------------------
${JAVA_CMD} -version
echo ------------------------------------------------------------------------

${JAVA_CMD} ${JAVA_OPT} -jar ${BASE_PATH}/lib/${KDTS_JAR} > ${LOG_PATH} 2>&1 &

# 进程ID
PID=$(ps -ef | grep "${KDTS_JAR}" | grep -v grep | awk '{ print $2 }')
KDTS_STARTUP_LOG="${KDTS_STARTUP_LOG}application pid: ${PID}\n"

# 启动日志追加到启动日志文件中
# echo -e ${KDTS_STARTUP_LOG} >> ${LOG_STARTUP_PATH}
# 打印启动脚本日志
echo -e ${KDTS_STARTUP_LOG}

# 查看日志
#tail -f ${LOG_PATH} &
#echo -e '可使用命令 tail -f ../logs/kdts-plus.log 查看程序运行情况'
echo See \"../logs/${KDTS}_${NOW_STR}.log\" or use the command-line \"tail -f ../logs/${KDTS}_${NOW_STR}.log\" for more detail.
