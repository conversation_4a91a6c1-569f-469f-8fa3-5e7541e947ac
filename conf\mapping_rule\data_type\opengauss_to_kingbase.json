[{"sourceType": {"name": "bpchar", "lengthMin": 1, "lengthMax": 10485760}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 10485760}}, {"sourceType": {"name": "char", "lengthMin": 1, "lengthMax": 10485760}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 10485760}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 10485760}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 10485760}}, {"sourceType": {"name": "smallserial", "precisionMin": 1, "precisionMax": 5, "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "serial", "precisionMin": 1, "precisionMax": 10, "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "bigserial", "precisionMin": 1, "precisionMax": 18, "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "int8"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "bigint"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "int"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "int4"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "smallint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "int2"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "float8"}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "float4"}, "targetType": {"name": "float4"}}, {"sourceType": {"name": "numeric", "precisionMin": 1, "precisionMax": 1000, "scaleMin": 0, "scaleMax": 1000}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 1000, "scaleMin": 0, "scaleMax": 1000}}, {"sourceType": {"name": "bytea"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "bool"}, "targetType": {"name": "bool"}}, {"sourceType": {"name": "interval"}, "targetType": {"name": "interval"}}, {"sourceType": {"name": "time"}, "targetType": {"name": "time"}}, {"sourceType": {"name": "date"}, "targetType": {"name": "date"}}, {"sourceType": {"name": "timestamp"}, "targetType": {"name": "timestamp"}}, {"sourceType": {"name": "text"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "geometry"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "geography"}, "targetType": {"name": "geography"}}, {"sourceType": {"name": "xml"}, "targetType": {"name": "xml"}}, {"sourceType": {"name": "timetz"}, "targetType": {"name": "timetz"}, "regexMatchReplace": true, "regularExpressions": ["timetz(.*)", "time(.*)with time zone"], "regularReplacements": ["timetz(${scale})", "time(${scale}) with time zone"]}, {"sourceType": {"name": "abstime"}, "targetType": {"name": "timestamp with time zone"}}, {"sourceType": {"name": "reltime"}, "targetType": {"name": "interval"}}, {"sourceType": {"name": "time with time zone"}, "targetType": {"name": "time with time zone"}, "regexMatchReplace": true, "regularExpressions": ["timetz(.*)", "time(.*)with time zone"], "regularReplacements": ["timetz(${scale})", "time(${scale}) with time zone"]}, {"sourceType": {"name": "timestamp with time zone"}, "targetType": {"name": "timestamp with time zone"}}, {"sourceType": {"name": "timestamptz"}, "targetType": {"name": "timestamptz"}, "regexMatchReplace": true, "regularExpressions": ["timestamptz(.*)", "timestamp(.*)with time zone"], "regularReplacements": ["timestamptz(${scale})", "timestamptz(${scale})"]}, {"sourceType": {"name": "timestamp with local time zone"}, "targetType": {"name": "timestamptz"}, "regexMatchReplace": true, "regularExpressions": ["timestamptz(.*)", "timestamp(.*)with local time zone"], "regularReplacements": ["timestamptz(${scale})", "timestamptz(${scale})"]}, {"sourceType": {"name": "numeric", "precisionMin": 1}, "targetType": {"name": "numeric", "precisionMin": 1}, "regexMatchReplace": true, "regularExpressions": ["numeric(.*)"], "regularReplacements": ["numeric(${precision},${scale})"]}, {"sourceType": {"name": "numeric", "precisionMin": 0, "precisionMax": 0}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 0}, "regexMatchReplace": true, "regularExpressions": ["numeric(.*)"], "regularReplacements": ["numeric"]}, {"sourceType": {"name": "geometry"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "geography"}, "targetType": {"name": "geography"}}, {"sourceType": {"name": "regprocedure"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "regproc"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "regdictionary"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "regoper"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "regoperator"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "regconfig"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "regtype"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "regclass"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "json"}, "targetType": {"name": "json"}}, {"sourceType": {"name": "jsonb"}, "targetType": {"name": "jsonb"}}, {"sourceType": {"name": "tsquery"}, "targetType": {"name": "tsquery"}}, {"sourceType": {"name": "tsvector"}, "targetType": {"name": "tsvector"}}, {"sourceType": {"name": "circle"}, "targetType": {"name": "circle"}}, {"sourceType": {"name": "box"}, "targetType": {"name": "box"}}, {"sourceType": {"name": "uuid"}, "targetType": {"name": "uuid"}}, {"sourceType": {"name": "cidr"}, "targetType": {"name": "cidr"}}, {"sourceType": {"name": "point"}, "targetType": {"name": "point"}}, {"sourceType": {"name": "lseg"}, "targetType": {"name": "lseg"}}, {"sourceType": {"name": "inet"}, "targetType": {"name": "inet"}}, {"sourceType": {"name": "<PERSON><PERSON>dr"}, "targetType": {"name": "<PERSON><PERSON>dr"}}, {"sourceType": {"name": "polygon"}, "targetType": {"name": "polygon"}}, {"sourceType": {"name": "path"}, "targetType": {"name": "path"}}, {"minTargetDbVersion": "V8R6C5B54", "sourceType": {"name": "RAW", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "raw", "lengthMin": 1, "lengthMax": 2000}}, {"minTargetDbVersion": "V7", "sourceType": {"name": "RAW"}, "targetType": {"name": "bytea"}}, {"minTargetDbVersion": "V8R6C5B54", "sourceType": {"name": "LONG RAW"}, "targetType": {"name": "long raw"}}, {"sourceType": {"name": "LONG RAW"}, "targetType": {"name": "bytea"}}]