[{"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["FUNCTION", "PROCEDURE"], "regexp": "NOT FENCED NOT SHIPPABLE", "replacement": "", "replaceType": "All"}, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["VIEW"], "regexp": "WITH \\(orientation=row,compression=no\\) ", "replacement": "", "replaceType": "All"}]