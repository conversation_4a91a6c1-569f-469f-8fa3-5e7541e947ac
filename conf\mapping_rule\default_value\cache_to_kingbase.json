[{}, null, {"columnTypeIncludes": ["VARCHAR"], "type": "RegexpReplace", "content": {"regexp": "\\Q$c(0)\\E", "replacement": "''", "replaceType": "All"}}, {"columnTypeIncludes": ["VARCHAR", "TIME"], "type": "RegexpReplace", "content": {"regexp": "^.{1}(.+).{1}$", "replacement": "'$1'", "replaceType": "All"}}, {"columnTypeIncludes": ["TIMESTAMP"], "type": "RegexpReplace", "content": {"regexp": "\\Q$$totimestamp^%qarfunc(\"\\E(.*)\",\"(.*)\"\\)", "replacement": "to_timestamp('$1','$2')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "replicate", "replacement": "repeat", "replaceType": "All"}}]