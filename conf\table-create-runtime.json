{"schemaName": "kdts_result", "tableName": "kdts_runtime_result", "default_Columns": [{"name": "task_name", "dbType": "<PERSON><PERSON><PERSON>(64)"}, {"name": "object_type", "dbType": "<PERSON><PERSON><PERSON>(32)"}, {"name": "object_name", "dbType": "<PERSON><PERSON><PERSON>(128)"}, {"name": "pre_num", "dbType": "text"}, {"name": "end_num", "dbType": "text"}, {"name": "data_total", "dbType": "long"}, {"name": "status", "dbType": "smallint"}]}