[{"sourceType": {"name": "<PERSON><PERSON><PERSON>", "matchExpressions": "length * 3 < 4000"}, "targetType": {"name": "VARCHAR2", "lengthExpressions": "length * 3"}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "matchExpressions": "length * 3 > 4000"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON><PERSON>", "lengthMin": 4001}, "targetType": {"name": "CLOB"}}]