[{"minTargetDbVersion": "V8R6", "columnTypeIncludes": ["TIMESTAMP"], "type": "RegexpReplace", "content": {"regexp": "CURRENT_TIMESTAMP[(]\\s*[)]", "replacement": "current_timestamp", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "['][\\t*\\s*\\r*][']", "replacement": "''", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+).NEXTVAL", "replacement": "nextval('${defaultValue}')", "replaceType": "All"}}]