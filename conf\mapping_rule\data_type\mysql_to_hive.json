[{"sourceType": {"name": "INT"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "INT unsigned"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "tinyint"}, "targetType": {"name": "tinyint"}}, {"sourceType": {"name": "tinyint unsigned"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "mediumint"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "mediumint unsigned"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "year"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "bigint"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "BIGINT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 38}}, {"sourceType": {"name": "bigint unsigned"}, "targetType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 38}}, {"sourceType": {"name": "float"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "float unsigned"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "double"}, "targetType": {"name": "double"}}, {"sourceType": {"name": "double unsigned"}, "targetType": {"name": "double"}}, {"sourceType": {"name": "smallint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "smallint unsigned"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 65355}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 65355}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 65356, "lengthMax": 10485760}, "targetType": {"name": "string"}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "text"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "tinytext"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(255)"}}, {"sourceType": {"name": "MEDIUMTEXT"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "time"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "json"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "longtext"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "enum"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "set"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "longblob"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "binary"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "varbinary"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "tinyblob"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "MEDIUMBLOB"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "blob"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "bit"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "date"}, "targetType": {"name": "date"}}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "datetime"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "linestring"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "geometrycollection"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "geometry"}, "targetType": {"name": "string"}}]