ACCESS, ADD, ALL, ALTER, AND, ANY, <PERSON><PERSON><PERSON><PERSON><PERSON>, AS, ASC, AUDIT,
BET<PERSON>EEN, BY, CHAR, <PERSON>ECK, <PERSON><PERSON>USTER, COLUMN, COMMENT, COMPRESS, CONNECT, CREATE,
CURRENT, DATE, DE<PERSON><PERSON><PERSON>, DEFAULT, <PERSON>LE<PERSON>, DESC, <PERSON><PERSON><PERSON><PERSON><PERSON>, DROP, <PERSON>LSE, EXCLUSIVE,
EXIS<PERSON>, FILE, FLOAT, FOR, FROM, GRANT, <PERSON><PERSON><PERSON>, HAVING, IDENTIFIED, IMMEDIATE,
IN, INCREMENT, INDEX, INITIAL, INSERT, INTEGER, INTERSECT, INTO, IS, LEVEL,
LIKE, LOCK, LONG, MAXEXTENTS, MINUS, MODE, MODIFY, NOAUDIT, NOCOMPRESS, NOT,
NOTFOUND, NOWAIT, NULL, NUMBER, OF, OFFLINE, ON, ONLINE, OPTION, OR,
ORDER, PCTFREE, PRIOR, PRIVILEGES, P<PERSON>BLIC, RAW, R<PERSON>AME, RES<PERSON>URCE, R<PERSON>VOKE, <PERSON>OW,
<PERSON>OW<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, R<PERSON><PERSON>M, ROWS, <PERSON><PERSON>CT, <PERSON><PERSON><PERSON>, SET, <PERSON>AR<PERSON>, SIZE, SMALLINT,
SQLBUF, START, SUCCESSFUL, SYNONYM, SYSDATE, TABLE, THEN, TO, TRIGGER, UID,
UNION, UNIQUE, UPDATE, USER, VALIDATE, VALUES, VARCHAR, VARCHAR2, VIEW, WHENEVER,
WHERE, WITH