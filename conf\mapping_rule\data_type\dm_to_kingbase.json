[{"sourceType": {"name": "BFILE"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 2000, "charUsedSupport": true}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "timestamp"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 4, "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 5, "precisionMax": 9, "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 10, "precisionMax": 18, "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 0}}, {"sourceType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 0, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "numeric"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 0, "scaleMin": -127, "scaleMax": -127}, "targetType": {"name": "numeric"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 38, "scaleMin": -84, "scaleMax": -1}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": -84, "scaleMax": -1, "attachNegativeScaleSize": false}}, {"sourceType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 38, "scaleMin": 1, "scaleMax": 127}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 1, "scaleMax": 127}}, {"sourceType": {"name": "NCHAR", "lengthMin": 1, "lengthMax": 1000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 1000}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 8000, "charUsedSupport": true}, "targetType": {"name": "varchar2", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "NVARCHAR2", "lengthMin": 1, "lengthMax": 2000, "charUsedSupport": true}, "targetType": {"name": "varchar2", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000, "charUsedSupport": true}, "targetType": {"name": "varchar2", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "NCLOB"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "DATETIME", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "timestamp", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "TIMESTAMP", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "timestamp", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "DEC", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "DEC", "precisionMin": 1, "precisionMax": 38}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38}}, {"sourceType": {"name": "DEC"}, "targetType": {"name": "numeric"}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 1, "precisionMax": 38}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38}}, {"sourceType": {"name": "NUMERIC"}, "targetType": {"name": "numeric"}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38}}, {"sourceType": {"name": "DECIMAL"}, "targetType": {"name": "numeric"}}, {"sourceType": {"name": "REAL"}, "targetType": {"name": "REAL"}}, {"sourceType": {"name": "DOUBLE PRECISION"}, "targetType": {"name": "DOUBLE PRECISION"}}, {"sourceType": {"name": "TIME WITH TIME ZONE"}, "targetType": {"name": "TIME WITH TIME ZONE"}, "regexMatchReplace": true, "regularExpressions": ["TIME(.*)WITH TIME ZONE"], "regularReplacements": ["TIME(${SCALE})WITH TIME ZONE"]}, {"sourceType": {"name": "INTERVALYM"}, "targetType": {"name": "INTERVALYM"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL(\\s*)YEAR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)MONTH", "INTERVAL(\\s*)YEAR(\\s*)TO(\\s*)MONTH", "INTERVAL(\\s*)YEAR", "INTERVAL(\\s*)MONTH"], "regularReplacements": ["INTERVAL YEAR TO MONTH", "INTERVAL YEAR TO MONTH", "INTERVAL YEAR", "INTERVAL MONTH"]}, {"sourceType": {"name": "INTERVALDS"}, "targetType": {"name": "INTERVALDS"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)HOUR(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)HOUR", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)HOUR(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)HOUR", "INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)MINUTE(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)MINUTE", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)MINUTE(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)MINUTE", "INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))", "INTERVAL(\\s*)DAY", "INTERVAL(\\s*)HOUR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)MINUTE(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)HOUR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)MINUTE", "INTERVAL(\\s*)HOUR(\\s*)TO(\\s*)MINUTE(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)HOUR(\\s*)TO(\\s*)MINUTE", "INTERVAL(\\s*)HOUR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)HOUR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)HOUR(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)HOUR(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)HOUR(\\s*)(\\((?<precision1>\\d*)\\))", "INTERVAL(\\s*)HOUR", "INTERVAL(\\s*)MINUTE(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)MINUTE(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)MINUTE(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)MINUTE(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)MINUTE(\\s*)(\\((?<precision1>\\d*)\\))", "INTERVAL(\\s*)MINUTE", "INTERVAL(\\s*)SECOND(\\s*)(\\((?<precision1>\\d*)\\))", "INTERVAL(\\s*)SECOND"], "regularReplacements": ["INTERVAL DAY TO HOUR(${PRECISION})", "INTERVAL DAY TO HOUR", "INTERVAL DAY TO HOUR(${PRECISION})", "INTERVAL DAY TO HOUR", "INTERVAL DAY TO MINUTE(${PRECISION})", "INTERVAL DAY TO MINUTE", "INTERVAL DAY TO MINUTE(${PRECISION})", "INTERVAL DAY TO MINUTE", "INTERVAL DAY TO SECOND(${PRECISION})", "INTERVAL DAY TO SECOND", "INTERVAL DAY TO SECOND(${PRECISION})", "INTERVAL DAY TO SECOND", "INTERVAL DAY", "INTERVAL DAY", "INTERVAL HOUR TO MINUTE(${PRECISION})", "INTERVAL HOUR TO MINUTE", "INTERVAL HOUR TO MINUTE(${PRECISION})", "INTERVAL HOUR TO MINUTE", "INTERVAL HOUR TO SECOND(${PRECISION})", "INTERVAL HOUR TO SECOND", "INTERVAL HOUR TO SECOND(${PRECISION})", "INTERVAL HOUR TO SECOND", "INTERVAL HOUR", "INTERVAL HOUR", "INTERVAL MINUTE TO SECOND(${PRECISION})", "INTERVAL MINUTE TO SECOND", "INTERVAL MINUTE TO SECOND(${PRECISION})", "INTERVAL MINUTE TO SECOND", "INTERVAL MINUTE", "INTERVAL MINUTE", "INTERVAL SECOND", "INTERVAL SECOND"]}, {"sourceType": {"name": "DATETIME WITH TIME ZONE"}, "targetType": {"name": "timestamptz"}, "regexMatchReplace": true, "regularExpressions": ["DATETIME(.*)WITH TIME ZONE"], "regularReplacements": ["timestamptz(${SCALE})"]}, {"sourceType": {"name": "INTERVAL MINUTE TO SECOND"}, "targetType": {"name": "INTERVAL MINUTE TO SECOND"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL MINUTE TO SECOND"], "regularReplacements": ["INTERVAL MINUTE TO SECOND(${PRECISION})"]}]