@echo off

set KDTS="kdts-plus-bs"

set "BIN_PATH=%cd%"
cd ..
set "BASE_PATH=%cd%"

rem PID文件
set "PID_FILE=%BASE_PATH%\pid"

echo %PID_FILE%

set "pid="
if exist %PID_FILE% (
	for /f "delims=" %%i in (%PID_FILE%) do (
		set "pid=%%i"
		goto :stop
	)
) else (
	echo %KDTS% is already stopped!
	pause
	goto :eof
)

:stop
echo now will kill process : pid %pid%

taskkill /f /pid %pid%

if exist %PID_FILE% (
    del /f %PID_FILE%
)

pause

