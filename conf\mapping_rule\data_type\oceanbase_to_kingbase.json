[{"sourceType": {"name": "BOOLEAN"}, "targetType": {"name": "boolean"}}, {"sourceType": {"name": "TINYINT UNSIGNED"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "SMALLINT"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "INT"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "INTEGER"}, "targetType": {"name": "integer"}}, {"sourceType": {"name": "BIGINT"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "LONG VARBINARY"}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 65, "scaleMin": 0, "scaleMax": 30}, "targetType": {"name": "decimal", "precisionMin": 0, "precisionMax": 65, "scaleMin": 0, "scaleMax": 30}}, {"sourceType": {"name": "DECIMAL UNSIGNED", "precisionMin": 0, "precisionMax": 65, "scaleMin": 0, "scaleMax": 30}, "targetType": {"name": "decimal", "precisionMin": 0, "precisionMax": 65, "scaleMin": 0, "scaleMax": 30}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "FLOAT UNSIGNED"}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "DOUBLE"}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "DOUBLE PRECISION"}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "DOUBLE UNSIGNED"}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "REAL"}, "targetType": {"name": "float4"}}, {"sourceType": {"name": "CHAR", "lengthMin": 0, "lengthMax": 0}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 1}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "LONG VARCHAR"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "TEXT"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "date"}}, {"sourceType": {"name": "TIMESTAMP", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "timestamp", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "TIME", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "time", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "geometry"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "geography"}, "targetType": {"name": "geography"}}]