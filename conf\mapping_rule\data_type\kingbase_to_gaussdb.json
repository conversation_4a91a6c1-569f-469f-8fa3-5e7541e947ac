[{"sourceType": {"name": "YMINTERVAL"}, "targetType": {"name": "interval year to month"}}, {"sourceType": {"name": "DSINTERVAL"}, "targetType": {"name": "interval day to second"}}, {"sourceType": {"name": "UROWID"}, "targetType": {"name": "<PERSON><PERSON><PERSON>"}}, {"sourceType": {"name": "BPCHARBYTE", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "BPCHARBYTE"}, "targetType": {"name": "char"}}, {"sourceType": {"name": "VARCHARBYTE", "lengthMin": 1, "lengthMax": 8188}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8188}}, {"sourceType": {"name": "VARCHARBYTE"}, "targetType": {"name": "<PERSON><PERSON><PERSON>"}}]