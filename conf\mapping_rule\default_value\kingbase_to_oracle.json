[{}, null, {"columnTypeIncludes": ["VARCHAR", "<PERSON><PERSON><PERSON>"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+)::varchar", "replacement": "${defaultValue}", "replaceType": "All"}}, {"columnTypeIncludes": ["int4", "int", "integer"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+)::integer", "replacement": "${defaultValue}", "replaceType": "All"}}, {"columnTypeIncludes": ["CHAR", "char", "BPCHAR", "bpchar"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+)::bpchar", "replacement": "${defaultValue}", "replaceType": "All"}}, {"columnTypeIncludes": ["TIMESTAMP", "timestamp"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+)::timestamp without time zone", "replacement": "${defaultValue}", "replaceType": "All"}}, {"columnTypeIncludes": ["NUMERIC", "numeric", "DECIMAL", "decimal"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+)::integer", "replacement": "${defaultValue}", "replaceType": "All"}}, {"columnTypeIncludes": ["NUMERIC", "numeric", "DECIMAL", "decimal"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+)::numeric", "replacement": "${defaultValue}", "replaceType": "All"}}, {"columnTypeIncludes": ["date"], "type": "RegexpReplace", "content": {"regexp": "\"sysdate\"\\(\\)", "replacement": "sysdate", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "now\\(\\)", "replacement": "systimestamp", "replaceType": "All"}}]