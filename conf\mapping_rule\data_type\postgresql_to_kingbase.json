[{"sourceType": {"name": "bpchar", "lengthMin": 1, "lengthMax": 10485760}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 10485760}}, {"sourceType": {"name": "char", "lengthMin": 1, "lengthMax": 10485760}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 10485760}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 10485760}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 10485760}}, {"sourceType": {"name": "smallserial", "precisionMin": 1, "precisionMax": 5, "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "serial", "precisionMin": 1, "precisionMax": 10, "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "bigserial", "precisionMin": 1, "precisionMax": 18, "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "int8", "precisionMin": 19, "precisionMax": 19, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "int4", "precisionMin": 10, "precisionMax": 10, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "int"}}, {"sourceType": {"name": "int2", "precisionMin": 5, "precisionMax": 5, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "float8", "precisionMin": 17, "precisionMax": 17, "scaleMin": 17, "scaleMax": 17}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "float4", "precisionMin": 8, "precisionMax": 8, "scaleMin": 8, "scaleMax": 8}, "targetType": {"name": "float4"}}, {"sourceType": {"name": "numeric", "precisionMin": 1, "precisionMax": 1000, "scaleMin": 0, "scaleMax": 1000}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 1000, "scaleMin": 0, "scaleMax": 1000}}, {"sourceType": {"name": "bytea"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "bool"}, "targetType": {"name": "bool"}}, {"sourceType": {"name": "interval"}, "targetType": {"name": "interval"}}, {"sourceType": {"name": "time"}, "targetType": {"name": "time"}}, {"sourceType": {"name": "date"}, "targetType": {"name": "date"}}, {"sourceType": {"name": "timestamp"}, "targetType": {"name": "timestamp"}}, {"sourceType": {"name": "text"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "geometry"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "geography"}, "targetType": {"name": "geography"}}, {"sourceType": {"name": "xml"}, "targetType": {"name": "xml"}}, {"sourceType": {"name": "timetz"}, "targetType": {"name": "timetz"}, "regexMatchReplace": true, "regularExpressions": ["timetz(.*)", "time(.*)with time zone"], "regularReplacements": ["timetz(${scale})", "time(${scale}) with time zone"]}, {"sourceType": {"name": "time with time zone"}, "targetType": {"name": "time with time zone"}, "regexMatchReplace": true, "regularExpressions": ["timetz(.*)", "time(.*)with time zone"], "regularReplacements": ["timetz(${scale})", "time(${scale}) with time zone"]}, {"sourceType": {"name": "timestamp with time zone"}, "targetType": {"name": "timestamp with time zone"}}, {"sourceType": {"name": "timestamptz"}, "targetType": {"name": "timestamptz"}, "regexMatchReplace": true, "regularExpressions": ["timestamptz(.*)", "timestamp(.*)with time zone"], "regularReplacements": ["timestamptz(${scale})", "timestamptz(${scale})"]}, {"sourceType": {"name": "timestamp with local time zone"}, "targetType": {"name": "timestamptz"}, "regexMatchReplace": true, "regularExpressions": ["timestamptz(.*)", "timestamp(.*)with local time zone"], "regularReplacements": ["timestamptz(${scale})", "timestamptz(${scale})"]}, {"sourceType": {"name": "numeric", "precisionMin": 1}, "targetType": {"name": "numeric", "precisionMin": 1}, "regexMatchReplace": true, "regularExpressions": ["numeric(.*)"], "regularReplacements": ["numeric(${precision},${scale})"]}, {"sourceType": {"name": "numeric", "precisionMin": 0, "precisionMax": 0}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 0}, "regexMatchReplace": true, "regularExpressions": ["numeric(.*)"], "regularReplacements": ["numeric"]}, {"sourceType": {"name": "geometry"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "geography"}, "targetType": {"name": "geography"}}, {"sourceType": {"name": "JSONB"}, "targetType": {"name": "JSONB"}}]