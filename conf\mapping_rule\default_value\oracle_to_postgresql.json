﻿[
    {},
    null,
    {
        "columnTypeIncludes": ["TIMESTAMP", "DATETIM<PERSON>", "DATE"],
        "type": "RegexpReplace",
        "content": {
            "regexp": "systimestamp",
            "replacement": "current_timestamp",
            "replaceType": "All"
        }
    },
    {
        "columnTypeIncludes": ["CHAR", "VARCHAR", "VARCHAR2", "TEXT"],
        "type": "RegexpReplace",
        "content": {
            "regexp": "to_char\\(sysdate,'yyyy-mm-dd hh24:mi:ss'\\)",
            "replacement": "to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS')",
            "replaceType": "All"
        }
    },
    {
        "columnTypeIncludes": ["CHAR", "VARCHAR", "VARCHAR2", "TEXT"],
        "type": "RegexpReplace",
        "content": {
            "regexp": "to_char\\s*\\(\\s*sysdate\\s*,\\s*'yyyy-mm-dd hh24:mi:ss'\\s*\\)",
            "replacement": "to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS')",
            "replaceType": "All"
        }
    },
    {
        "columnTypeIncludes": ["CHAR", "VARCHAR", "VARCHAR2", "TEXT"],
        "type": "RegexpReplace",
        "content": {
            "regexp": "to_char\\s*\\(\\s*sysdate\\s*,\\s*'yyyy-MM-dd HH24:mi:ss'\\s*\\)",
            "replacement": "to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS')",
            "replaceType": "All"
        }
    },
    {
        "columnTypeIncludes": ["CHAR", "VARCHAR", "VARCHAR2", "TEXT"],
        "type": "RegexpReplace",
        "content": {
            "regexp": "to_char\\s*\\(\\s*sysdate\\s*,\\s*'YYYY-MM-DD HH24:MI:SS'\\s*\\)",
            "replacement": "to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS')",
            "replaceType": "All"
        }
    },
    {
        "columnTypeIncludes": ["CHAR", "VARCHAR", "VARCHAR2", "TEXT"],
        "type": "RegexpReplace",
        "content": {
            "regexp": "to_char\\s*\\(\\s*sysdate\\s*,\\s*'[^']*'\\s*\\)",
            "replacement": "to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS')",
            "replaceType": "All"
        }
    },
    {
        "columnTypeIncludes": ["TIMESTAMP", "DATETIME", "DATE", "CHAR", "VARCHAR", "VARCHAR2"],
        "type": "RegexpReplace",
        "content": {
            "regexp": "\\bsysdate\\b",
            "replacement": "current_timestamp",
            "replaceType": "All"
        }
    },
    {
        "columnTypeIncludes": ["CHAR", "VARCHAR", "VARCHAR2", "TEXT"],
        "type": "RegexpReplace",
        "content": {
            "regexp": "to_char\\(sysdate,'yyyy-mm-dd hh24:mi:ss'\\)",
            "replacement": "CURRENT_TIMESTAMP::TEXT",
            "replaceType": "All"
        }
    }
]
