[{}, null, {"tableIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\s*TO_DATE\\s*\\(\\s*'(?<date>.*?)'\\s*,\\s*'\\s*SYYYY-MM-DD HH24:MI:SS'\\s*\\)\\s*", "replacement": "STR_TO_DATE('$1', '%Y-%m-%d %H:%i:%s')", "replaceType": "All"}}, {"tableIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\s*?(\\s*`AS_OF_DATE`\\s*?)\\s*", "replacement": "(to_days(`AS_OF_DATE`))", "replaceType": "All"}}, {"tableIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\s*'(?<date>.*?)'\\s*", "replacement": "to_days('$1')", "replaceType": "All"}}]