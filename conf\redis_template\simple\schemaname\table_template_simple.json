{"schema": "*", "table": "b_business_message_template", "primary": {"keyTemplate": "b_business_message_template::<tmp_id>", "valueType": "json", "valueColumns": []}, "indexes": [{"keyTemplate": "b_business_message_template::<tmp_station_name>", "valueType": "json", "valueColumns": [], "joinSymbol": ","}, {"keyTemplate": "b_business_message_template::<tmp_station_name>::<tmp_type>", "valueType": "json", "valueColumns": [], "joinSymbol": ","}, {"keyTemplate": "b_business_message_template::<tmp_station_code>", "valueType": "json", "valueColumns": [], "joinSymbol": ","}, {"keyTemplate": "b_business_message_template::<tmp_station_code>::<tmp_type>", "valueType": "json", "valueColumns": [], "joinSymbol": ","}], "filterExpression": "tmp_valid == '0'", "expire": -1}