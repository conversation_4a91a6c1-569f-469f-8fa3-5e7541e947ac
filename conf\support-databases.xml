<!-- 配置转换程序支持的数据库列表，不要修改格式 -->
<ArrayList>
    <item>
        <db_type>ORACLE</db_type>
        <db_version>19c</db_version>
        <port>1521</port>
        <driver>oracle.jdbc.OracleDriver</driver>
        <urls>
            <urls>
                <name>SID</name>
                <value>jdbc:oracle:thin:@{0}:{1}:{2}</value>
            </urls>
            <urls>
                <name>ServiceName</name>
                <value>jdbc:oracle:thin:@{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>ORACLE</db_type>
        <db_version>12c</db_version>
        <port>1521</port>
        <driver>oracle.jdbc.OracleDriver</driver>
        <urls>
            <urls>
                <name>SID</name>
                <value>jdbc:oracle:thin:@{0}:{1}:{2}</value>
            </urls>
            <urls>
                <name>ServiceName</name>
                <value>jdbc:oracle:thin:@{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>ORACLE</db_type>
        <db_version>11g</db_version>
        <port>1521</port>
        <driver>oracle.jdbc.OracleDriver</driver>
        <urls>
            <urls>
                <name>SID</name>
                <value>jdbc:oracle:thin:@{0}:{1}:{2}</value>
            </urls>
            <urls>
                <name>ServiceName</name>
                <value>jdbc:oracle:thin:@{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>MYSQL</db_type>
        <db_version>5.1</db_version>
        <port>3306</port>
        <driver>com.mysql.jdbc.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:mysql://{0}:{1}/{2}</value>
                <params>
                    <params><name>zeroDateTimeBehavior</name><value>convertToNull</value></params>
                    <params><name>useCursorFetch</name><value>false</value></params>
                    <params><name>yearIsDateType</name><value>true</value></params>
                    <params><name>tinyInt1isBit</name><value>false</value></params>
                    <params><name>useUnicode</name><value>true</value></params>
                    <params><name>characterEncoding</name><value>utf-8</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>MYSQL</db_type>
        <db_version>5.5</db_version>
        <port>3306</port>
        <driver>com.mysql.jdbc.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:mysql://{0}:{1}/{2}</value>
                <params>
                    <params><name>zeroDateTimeBehavior</name><value>convertToNull</value></params>
                    <params><name>useCursorFetch</name><value>false</value></params>
                    <params><name>yearIsDateType</name><value>true</value></params>
                    <params><name>tinyInt1isBit</name><value>false</value></params>
                    <params><name>useUnicode</name><value>true</value></params>
                    <params><name>characterEncoding</name><value>utf-8</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>MYSQL</db_type>
        <db_version>5.7</db_version>
        <port>3306</port>
        <driver>com.mysql.cj.jdbc.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:mysql://{0}:{1}/{2}</value>
                <params>
                    <params><name>zeroDateTimeBehavior</name><value>convertToNull</value></params>
                    <params><name>useCursorFetch</name><value>false</value></params>
                    <params><name>yearIsDateType</name><value>true</value></params>
                    <params><name>tinyInt1isBit</name><value>false</value></params>
                    <params><name>useUnicode</name><value>true</value></params>
                    <params><name>characterEncoding</name><value>utf-8</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>MYSQL</db_type>
        <db_version>8.0</db_version>
        <port>3306</port>
        <driver>com.mysql.cj.jdbc.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:mysql://{0}:{1}/{2}</value>
                <params>
                    <params><name>zeroDateTimeBehavior</name><value>convertToNull</value></params>
                    <params><name>useCursorFetch</name><value>false</value></params>
                    <params><name>yearIsDateType</name><value>true</value></params>
                    <params><name>tinyInt1isBit</name><value>false</value></params>
                    <params><name>useUnicode</name><value>true</value></params>
                    <params><name>characterEncoding</name><value>utf-8</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>DM</db_type>
        <db_version>8</db_version>
        <port>5236</port>
        <driver>dm.jdbc.driver.DmDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:dm://{0}:{1}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2000</db_version>
        <port>1433</port>
        <driver>net.sourceforge.jtds.jdbc.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:jtds:sqlserver://{0}:{1};DatabaseName={2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2005</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2008</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2012</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2014</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2016</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2017</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>SQLSERVER</db_type>
        <db_version>2019</db_version>
        <port>1433</port>
        <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:sqlserver://{0}:{1};DatabaseName={2}</value>
                <params>
                    <params>
                        <name>encrypt</name>
                        <value>false</value>
                    </params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>GBASE</db_type>
        <db_version>8g</db_version>
        <port>5258</port>
        <driver>com.gbase.jdbc.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:gbase://{0}:{1}/{2}</value>
                <params>
                    <params><name>zeroDateTimeBehavior</name><value>convertToNull</value></params>
                    <params><name>nullCatalogMeansCurrent</name><value>true</value></params>
                    <params><name>yearIsDateType</name><value>true</value></params>
                    <params><name>tinyInt1isBit</name><value>false</value></params>
                    <params><name>useUnicode</name><value>true</value></params>
                    <params><name>characterEncoding</name><value>utf-8</value></params>
                </params>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>GBASE</db_type>
        <db_version>8s</db_version>
        <port>5258</port>
        <driver>com.gbasedbt.jdbc.IfxDriver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:gbasedbt-sqli://{0}:{1}/{2}</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>KINGBASE</db_type>
        <db_version>V8R6</db_version>
        <port>54321</port>
        <driver>com.kingbase8.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:kingbase8://{0}:{1}/{2}?clientEncoding=utf8</value>
            </urls>
            <urls>
                <name>UnixDomainSocket</name>
                <value>jdbc:kingbase8:///{2}?clientEncoding=utf8&amp;socketFactory=org.newsclub.net.unix.AFUNIXSocketFactory$FactoryArg&amp;socketFactoryArg=/tmp/.s.KINGBASE.{1}&amp;sslMode=disable</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>KINGBASE</db_type>
        <db_version>V8R3</db_version>
        <port>54321</port>
        <driver>com.kingbase83.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:kingbase83://{0}:{1}/{2}?clientEncoding=utf8</value>
            </urls>
            <urls>
                <name>UnixDomainSocket</name>
                <value>jdbc:kingbase83:///{2}?clientEncoding=utf8&amp;socketFactory=org.newsclub.net.unix.AFUNIXSocketFactory$FactoryArg&amp;socketFactoryArg=/tmp/.s.KINGBASE.{1}&amp;sslMode=disable</value>
            </urls>
        </urls>
    </item>
    <item>
        <db_type>KINGBASE</db_type>
        <db_version>V8R2</db_version>
        <port>54321</port>
        <driver>com.kingbase8.Driver</driver>
        <urls>
            <urls>
                <name>JDBC</name>
                <value>jdbc:kingbase8://{0}:{1}/{2}?clientEncoding=utf8</value>
            </urls>
            <urls>
                <name>UnixDomainSocket</name>
                <value>jdbc:kingbase8:///{2}?clientEncoding=utf8&amp;socketFactory=org.newsclub.net.unix.AFUNIXSocketFactory$FactoryArg&amp;socketFactoryArg=/tmp/.s.KINGBASE.{1}&amp;sslMode=disable</value>
            </urls>
        </urls>
    </item>
</ArrayList>
