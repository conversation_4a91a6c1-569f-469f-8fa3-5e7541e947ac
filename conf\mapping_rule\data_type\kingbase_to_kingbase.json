[{"sourceType": {"name": "smallint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "tinyint"}, "targetType": {"name": "tinyint"}}, {"sourceType": {"name": "timestamp", "scaleMin": 6, "scaleMax": 6}, "targetType": {"name": "timestamp"}}, {"sourceType": {"name": "INT2"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "INT4"}, "targetType": {"name": "integer"}}, {"sourceType": {"name": "BOX"}, "targetType": {"name": "BOX"}}, {"sourceType": {"name": "PATH"}, "targetType": {"name": "PATH"}}, {"sourceType": {"name": "LINE"}, "targetType": {"name": "LINE"}}, {"sourceType": {"name": "CIRCLE"}, "targetType": {"name": "CIRCLE"}}, {"sourceType": {"name": "MACADDR"}, "targetType": {"name": "MACADDR"}}, {"sourceType": {"name": "MACADDR8"}, "targetType": {"name": "MACADDR8"}}, {"sourceType": {"name": "UUID"}, "targetType": {"name": "UUID"}}, {"sourceType": {"name": "DSINTERVAL"}, "targetType": {"name": "DSINTERVAL"}}, {"sourceType": {"name": "YMINTERVAL"}, "targetType": {"name": "YMINTERVAL"}}, {"sourceType": {"name": "POLYGON"}, "targetType": {"name": "POLYGON"}}, {"sourceType": {"name": "TSQUERY"}, "targetType": {"name": "TSQUERY"}}, {"sourceType": {"name": "LSEG"}, "targetType": {"name": "LSEG"}}, {"sourceType": {"name": "JSON"}, "targetType": {"name": "JSON"}}, {"sourceType": {"name": "JSONB"}, "targetType": {"name": "JSONB"}}, {"sourceType": {"name": "INTEGER"}, "targetType": {"name": "integer"}}, {"sourceType": {"name": "int"}, "targetType": {"name": "integer"}}, {"maxSourceDbVersion": "V8R3", "sourceType": {"name": "FLOAT8"}, "targetType": {"name": "FLOAT8"}}, {"maxSourceDbVersion": "V8R3", "sourceType": {"name": "FLOAT4"}, "targetType": {"name": "FLOAT4"}}, {"sourceType": {"name": "INET"}, "targetType": {"name": "INET"}}, {"sourceType": {"name": "CIDR"}, "targetType": {"name": "CIDR"}}, {"sourceType": {"name": "REGTYPE"}, "targetType": {"name": "REGTYPE"}}, {"sourceType": {"name": "REGROLE"}, "targetType": {"name": "REGROLE"}}, {"sourceType": {"name": "REGPROCEDURE"}, "targetType": {"name": "REGPROCEDURE"}}, {"sourceType": {"name": "REGPROC"}, "targetType": {"name": "REGPROC"}}, {"sourceType": {"name": "REGOPERATOR"}, "targetType": {"name": "REGOPERATOR"}}, {"sourceType": {"name": "REGOPER"}, "targetType": {"name": "REGOPER"}}, {"sourceType": {"name": "REGNAMESPACE"}, "targetType": {"name": "REGNAMESPACE"}}, {"sourceType": {"name": "REGDICTIONARY"}, "targetType": {"name": "REGDICTIONARY"}}, {"sourceType": {"name": "REGCONFIG"}, "targetType": {"name": "REGCONFIG"}}, {"sourceType": {"name": "REGCLASS"}, "targetType": {"name": "REGCLASS"}}, {"sourceType": {"autoIncrement": true, "name": "SMALLSERIAL"}, "targetType": {"name": "SMALLSERIAL"}}, {"sourceType": {"autoIncrement": true, "name": "BIGSERIAL"}, "targetType": {"name": "BIGSERIAL"}}, {"sourceType": {"autoIncrement": true, "name": "SERIAL"}, "targetType": {"name": "SERIAL"}}, {"sourceType": {"name": "PG_CATALOG.INTERVAL"}, "targetType": {"name": "PG_CATALOG.INTERVAL"}}, {"sourceType": {"name": "TIME WITH TIME ZONE"}, "targetType": {"name": "TIME WITH TIME ZONE"}}, {"sourceType": {"name": "TIMESTAMP WITH TIME ZONE"}, "targetType": {"name": "TIMESTAMP WITH TIME ZONE"}}, {"sourceType": {"name": "TIMESTAMPTZ"}, "targetType": {"name": "TIMESTAMPTZ"}, "regexMatchReplace": true, "regularExpressions": ["TIMESTAMPTZ(.*)", "TIMESTAMP(.*)WITH TIME ZONE"], "regularReplacements": ["TIMESTAMPTZ(${SCALE})", "TIMESTAMPTZ(${SCALE})"]}, {"sourceType": {"name": "VARCHARBYTE"}, "targetType": {"name": "CHARACTER VARYING"}, "regexMatchReplace": true, "regularExpressions": ["VARCHARBYTE"], "regularReplacements": ["CHARACTER VARYING(${PRECISION} BYTE)"]}, {"sourceType": {"name": "BPCHARBYTE", "precisionMin": 2147483647}, "targetType": {"name": "BPCHARBYTE"}}, {"sourceType": {"name": "BPCHARBYTE", "precisionMin": 1}, "targetType": {"name": "CHARACTER"}, "regexMatchReplace": true, "regularExpressions": ["BPCHARBYTE"], "regularReplacements": ["CHARACTER(${PRECISION} BYTE)"]}, {"minTargetDbVersion": "V8R6C7", "sourceType": {"name": "BPCHARBYTE"}, "targetType": {"name": "BPCHARBYTE"}}, {"maxSourceDbVersion": "V8R3", "sourceType": {"name": "VARBIT"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "TSVECTOR"}, "targetType": {"name": "tsvector"}}, {"sourceType": {"name": "TEXT"}, "targetType": {"name": "text"}}, {"maxSourceDbVersion": "V8R6", "sourceType": {"name": "float8"}, "targetType": {"name": "FLOAT8"}}, {"maxSourceDbVersion": "V8R6", "sourceType": {"name": "float4"}, "targetType": {"name": "FLOAT4"}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 1}, "targetType": {"name": "NUMERIC", "precisionMin": 1}, "regexMatchReplace": true, "regularExpressions": ["NUMERIC(.*)"], "regularReplacements": ["NUMERIC(${PRECISION},${SCALE})"]}, {"sourceType": {"name": "NUMERIC", "precisionMin": 0, "precisionMax": 0}, "targetType": {"name": "NUMERIC", "precisionMin": 0, "precisionMax": 0}, "regexMatchReplace": true, "regularExpressions": ["NUMERIC"], "regularReplacements": ["NUMERIC"]}, {"sourceType": {"name": "MONEY"}, "targetType": {"name": "money"}}, {"sourceType": {"name": "POINT"}, "targetType": {"name": "point"}}, {"sourceType": {"name": "NAME"}, "targetType": {"name": "NAME"}}, {"sourceType": {"name": "RAW", "lengthMin": 1, "lengthMax": 32767}, "targetType": {"name": "raw", "lengthMin": 1, "lengthMax": 32767}}, {"sourceType": {"name": "RAW"}, "targetType": {"name": "raw"}}, {"sourceType": {"name": "geometry"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "geography"}, "targetType": {"name": "geography"}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "DATE"}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 10485761}, "targetType": {"name": "<PERSON><PERSON><PERSON>"}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1}}, {"sourceType": {"name": "datetime"}, "targetType": {"name": "datetime"}}]