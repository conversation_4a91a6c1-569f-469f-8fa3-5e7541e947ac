[{"sourceType": {"name": "BLOB"}, "targetType": {"name": "longblob"}}, {"sourceType": {"name": "DATETIME WITH TIME ZONE"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 30}}, {"sourceType": {"name": "DEC", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 30}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 30}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 30}}, {"sourceType": {"name": "CHARACTER", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "CHARACTER", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "CHARACTER", "lengthMin": 256, "lengthMax": 2147483647}, "targetType": {"name": "text"}}, {"sourceType": {"name": "char", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "char", "lengthMin": 256, "lengthMax": 2147483647}, "targetType": {"name": "text"}}, {"sourceType": {"name": "VARCHAR"}, "targetType": {"name": "VARCHAR"}, "regexMatchReplace": true, "regularExpressions": ["VARCHAR"], "regularReplacements": ["VARCHAR(${COLUMN_SIZE})"]}, {"sourceType": {"name": "DATETIME", "scaleMin": 0, "scaleMax": 6}, "targetType": {"name": "DATETIME", "scaleMin": 0, "scaleMax": 6}}, {"sourceType": {"name": "text"}, "targetType": {"name": "longtext"}}, {"sourceType": {"name": "BYTE"}, "targetType": {"name": "TINYINT"}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "REAL"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "DOUBLE"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "TIME WITH TIME ZONE"}, "targetType": {"name": "TIME"}, "regexMatchReplace": true, "regularExpressions": ["TIME(.*)WITH TIME ZONE"], "regularReplacements": ["TIME(${SCALE})"]}, {"sourceType": {"name": "TIMESTAMP WITH LOCAL TIME ZONE"}, "targetType": {"name": "datetime"}, "regexMatchReplace": true, "regularExpressions": ["TIMESTAMP(.*)WITH LOCAL TIME ZONE"], "regularReplacements": ["datetime(${SCALE})"]}, {"sourceType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000, "charUsedSupport": true}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "NVARCHAR2", "lengthMin": 1, "lengthMax": 65535}, "targetType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 65535}}, {"sourceType": {"name": "NVARCHAR2", "lengthMin": 65536, "lengthMax": 2147483647}, "targetType": {"name": "TEXT"}}]