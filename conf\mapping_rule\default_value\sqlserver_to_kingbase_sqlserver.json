[{}, null, {"columnTypeIncludes": ["*"], "minTargetDbVersion": "V9", "type": "RegexpReplace", "content": {"regexp": "(?i)newid", "replacement": "newid", "replaceType": "All"}}, {"columnTypeIncludes": ["bit"], "type": "RegexpReplace", "content": {"regexp": "^0&", "replacement": "0", "replaceType": "All"}}, {"columnTypeIncludes": ["bit"], "type": "RegexpReplace", "content": {"regexp": "^1&", "replacement": "1", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?\\s*CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(\\s*21\\s*\\)\\s*\\)\\s*\\)?", "replacement": "to_char(now(),'YYYYMMDD')+to_char(now(),'HH:MI')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(112\\)\\s*\\)\\s*\\+\\s*CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(114\\)\\s*\\)\\)?", "replacement": "to_char(now(),'YYYYMMDD')+to_char(now(),'HH24:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(112\\)\\s*\\)\\s*\\+\\s*CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(114\\)\\s*\\)\\)?", "replacement": "to_char(now(),'YYYYMMDD')+to_char(now(),'HH24:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(112\\)\\s*\\)\\s*\\+\\s*CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(108\\)\\s*\\)\\)?", "replacement": "to_char(now(),'YYYYMMDD')+to_char(now(),'HH:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(112\\)\\s*\\)\\s*\\+\\s*CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(108\\)\\s*\\)\\)?", "replacement": "to_char(now(),'YYYYMMDD')+to_char(now(),'HH:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(112\\)\\s*\\)\\s*\\+\\s*CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(8\\)\\s*\\)\\)?", "replacement": "to_char(now(),'YYYYMMDD')+to_char(now(),'HH:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(112\\)\\s*\\)\\s*\\+\\s*CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(8\\)\\s*\\)\\)?", "replacement": "to_char(now(),'YYYYMMDD')+to_char(now(),'HH:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(112\\)\\s*\\)\\s*\\+\\s*CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(8\\)\\s*\\)\\)?", "replacement": "to_char(now(),'YYYYMMDD')+to_char(now(),'HH:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(112\\)\\s*\\)\\s*\\+\\s*CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(24\\)\\s*\\)\\)?", "replacement": "to_char(now(),'YYYYMMDD')+to_char(now(),'HH:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(121\\)\\s*\\)\\s*\\)\\)?", "replacement": "to_char(now(),'YYYYMMDD')+to_char(now(),'HH:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["<PERSON><PERSON><PERSON>"], "type": "RegexpReplace", "content": {"regexp": "\\s*\\(\\s*\\(\\s*(.*)\\s*\\)\\s*\\)", "replacement": "'$1'", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(120\\)\\s*\\)\\s*,\\s*\\(0\\)\\s*\\)\\)?", "replacement": "to_char(now(),'YYYYMMDD')+to_char(now(),'HH:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(112\\)\\s*\\)\\s*\\+\\s*CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(114\\)\\s*\\)\\)?", "replacement": "to_char(now(),'YYYYMMDD')+to_char(now(),'HH24:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(112\\)\\s*\\)\\s*\\+\\s*\\'\\s*\\'\\s*\\)\\s*\\+\\s*CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(114\\)\\s*\\)?", "replacement": "to_char(now(),'YYYYMMDD')+' '+to_char(now(),'HH24:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(\\s*(\\d+)\\s*\\)\\s*\\)\\)?", "replacement": "CONVERT([varchar]($1), getdate(), $2)", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(112\\)\\s*\\)\\s*\\)?", "replacement": "to_char(now(),'YYYYMMDD')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(112\\)\\s*\\)\\s*\\)?", "replacement": "to_char(now(),'YYYYMMDD')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(114\\)\\s*\\)\\s*\\)?", "replacement": "to_char(now(),'HH24:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*char\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(114\\)\\s*\\)\\s*\\)?", "replacement": "to_char(now(),'HH24:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\s*\\(\\s*\\(\\s*(\\s*\\d+\\s*)\\s*\\)\\s*\\)", "replacement": "$1", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "maxTargetDbVersion": "V8R6C7", "type": "RegexpReplace", "content": {"regexp": "\\(?\\s*getdate\\s*\\(\\s*\\)\\s*\\)?", "replacement": "now()", "replaceType": "All"}}]