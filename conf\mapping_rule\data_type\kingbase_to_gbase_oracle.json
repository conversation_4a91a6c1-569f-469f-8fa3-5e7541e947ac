[{"sourceType": {"name": "CHAR", "matchExpressions": "length * 3 <= 32767"}, "targetType": {"name": "char", "lengthExpressions": "length * 3"}}, {"sourceType": {"name": "CHAR", "matchExpressions": "length * 3 > 32767"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "CHARBYTE", "matchExpressions": "length * 3 <= 32767"}, "targetType": {"name": "char", "lengthExpressions": "length * 3"}}, {"sourceType": {"name": "CHARBYTE", "matchExpressions": "length * 3 > 32767"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "BPCHAR", "matchExpressions": "length * 3 <= 32767"}, "targetType": {"name": "char", "lengthExpressions": "length * 3"}}, {"sourceType": {"name": "BPCHAR", "matchExpressions": "length * 3 > 32767"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "BPCHARBYTE", "matchExpressions": "length * 3 <= 32767"}, "targetType": {"name": "char", "lengthExpressions": "length * 3"}}, {"sourceType": {"name": "BPCHARBYTE", "matchExpressions": "length * 3 > 32767"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "VARCHARBYTE", "matchExpressions": "length * 3 <= 32765"}, "targetType": {"name": "VARCHAR", "lengthExpressions": "length * 3"}}, {"sourceType": {"name": "VARCHARBYTE", "matchExpressions": "length * 3 > 32765"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "VARCHAR", "matchExpressions": "length * 3 <= 32765"}, "targetType": {"name": "VARCHAR", "lengthExpressions": "length * 3"}}, {"sourceType": {"name": "VARCHAR", "matchExpressions": "length * 3 > 32765"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "VARCHAR"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "numeric", "precisionMin": 0, "scaleMin": 0}, "targetType": {"name": "DECIMAL", "precisionMin": 0, "scaleMin": 0}}, {"sourceType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 1, "scaleMax": 38}, "targetType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38, "scaleMin": 1, "scaleMax": 38}}, {"sourceType": {"name": "numeric", "precisionMin": 39}, "targetType": {"name": "DECIMAL", "precisionMin": 39}}, {"sourceType": {"name": "TIMESTAMP", "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "datetime year to second"}}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "datetime year to fraction"}, "regexMatchReplace": true, "regularExpressions": ["TIMESTAMP(.*)"], "regularReplacements": ["datetime year to fraction(${scale})"]}, {"sourceType": {"name": "date"}, "targetType": {"name": "datetime year to fraction"}}]