[{"sourceType": {"name": "TINYINT"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "INT2"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "CHARACTER", "lengthMin": 1, "lengthMax": 20000}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 20000}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "NCHAR", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "CHAR", "matchExpressions": "length  > 4000"}, "targetType": {"name": "NTEXT"}}, {"sourceType": {"name": "NCHAR", "matchExpressions": "length  > 4000"}, "targetType": {"name": "NTEXT"}}, {"sourceType": {"name": "SERIAL", "autoIncrement": true}, "targetType": {"name": "INT IDENTITY"}}, {"sourceType": {"name": "OID"}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "REG"}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "BIGSERIAL", "autoIncrement": true}, "targetType": {"name": "BIGINT IDENTITY"}}, {"sourceType": {"name": "SMALLSERIAL", "autoIncrement": true}, "targetType": {"name": "SMALLINT IDENTITY"}}, {"sourceType": {"name": "SMALLSERIAL", "autoIncrement": true}, "targetType": {"name": "SMALLINT IDENTITY"}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "NVARCHAR", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "VARCHAR", "matchExpressions": "length  > 4000"}, "targetType": {"name": "NTEXT"}}, {"sourceType": {"name": "NVARCHAR", "matchExpressions": "length  > 4000"}, "targetType": {"name": "NTEXT"}}, {"sourceType": {"name": "BPCHAR", "matchExpressions": "length * 2 > 4000"}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "BPCHAR", "matchExpressions": "length * 2 <= 4000"}, "targetType": {"name": "CHAR", "lengthExpressions": "length * 2"}}, {"sourceType": {"name": "NCLOB"}, "targetType": {"name": "NTEXT"}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "NTEXT"}}, {"sourceType": {"name": "NAME"}, "targetType": {"name": "VARCHAR(64)"}}, {"sourceType": {"name": "CIDR"}, "targetType": {"name": "VARCHAR(20)"}}, {"sourceType": {"name": "TEXT"}, "targetType": {"name": "NTEXT"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "IMAGE"}}, {"sourceType": {"name": "INT4"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "BOOL"}, "targetType": {"name": "BIT"}}, {"sourceType": {"name": "TIMETZ"}, "targetType": {"name": "TIME"}}, {"sourceType": {"name": "INT8"}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "INTERVAL"}, "targetType": {"name": "VARCHAR(50)"}}, {"sourceType": {"name": "TINTERVAL"}, "targetType": {"name": "VARCHAR(50)"}}, {"sourceType": {"name": "REAL"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "DOUBLE"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "FLOAT4"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "FLOAT8"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "ABSTIME"}, "targetType": {"name": "DATETIME2"}}, {"sourceType": {"name": "TIMESTAMP WITH TIMEZONE"}, "targetType": {"name": "DATETIMEOFFSET"}}, {"sourceType": {"name": "TIMESTAMPTZ"}, "targetType": {"name": "DATETIMEOFFSET"}}, {"sourceType": {"name": "TIMESTAMP WITHOUT TIMEZONE"}, "targetType": {"name": "DATETIME2"}}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "DATETIME2"}}, {"sourceType": {"name": "BIT", "lengthMin": 1, "lengthMax": 20000}, "targetType": {"name": "BINARY", "lengthMin": 1, "lengthMax": 20000}}, {"sourceType": {"name": "VARBIT"}, "targetType": {"name": "IMAGE"}}, {"sourceType": {"name": "INET"}, "targetType": {"name": "VARCHAR(50)"}}, {"sourceType": {"name": "MONEY"}, "targetType": {"name": "VARCHAR(100)"}}, {"sourceType": {"name": "NUMERIC", "matchExpressions": "precision  > 38"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "NUMERIC", "matchExpressions": "precision  < 38"}, "targetType": {"name": "NUMERIC", "precisionExpressions": "38", "scaleExpressions": "scale"}}, {"sourceType": {"name": "BYTEA"}, "targetType": {"name": "IMAGE"}}, {"sourceType": {"name": "POINT"}, "targetType": {"name": "VARCHAR(46)"}}, {"sourceType": {"name": "LINE"}, "targetType": {"name": "VARCHAR(30)"}}, {"sourceType": {"name": "POLYGON"}, "targetType": {"name": "VARCHAR(197)"}}, {"sourceType": {"name": "BOX"}, "targetType": {"name": "VARCHAR(97)"}}, {"sourceType": {"name": "CIRCLE"}, "targetType": {"name": "VARCHAR(73)"}}, {"sourceType": {"name": "LSEG"}, "targetType": {"name": "VARCHAR(100)"}}, {"sourceType": {"name": "PATH"}, "targetType": {"name": "VARCHAR(140)"}}, {"sourceType": {"name": "RASTER"}, "targetType": {"name": "NTEXT"}}, {"sourceType": {"name": "GEOGRAPHY"}, "targetType": {"name": "NTEXT"}}, {"sourceType": {"name": "GEOMETRY"}, "targetType": {"name": "NTEXT"}}, {"sourceType": {"name": "MACADDR"}, "targetType": {"name": "VARCHAR(50)"}}, {"sourceType": {"name": "TSQUERY"}, "targetType": {"name": "VARCHAR(2046)"}}, {"sourceType": {"name": "TSVECTOR"}, "targetType": {"name": "VARCHAR(2046)"}}, {"sourceType": {"name": "UUID"}, "targetType": {"name": "VARCHAR(2046)"}}, {"sourceType": {"name": "JSON"}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "RELTIME"}, "targetType": {"name": "VARCHAR(50)"}}]