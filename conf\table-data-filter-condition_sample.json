[{"tables": ["TEST1"], "alias": "t", "content": "WHERE t.ID BETWEEN 1 AND 2000"}, {"schemaIncludes": ["Schema1"], "schemaExcludes": null, "tables": ["TEST2", "TEST3"], "alias": "t", "content": "JOIN Schema1.TABLE1 m ON t.REF_ID = m.ID AND m.ID BETWEEN 1 AND 1000000 WHERE t.STATUS = 0"}, {}, null, {"schemaIncludes": ["*"], "schemaExcludes": [], "tables": ["TABLE1"], "fullSQL": true, "content": "SELECT COl1, COL2, COL3 FROM TABLE1 WHERE BIZ_TIME >= TO_DATE('2001-01-01 00:00:00','yyyy-mm-dd hh24:mi:ss') AND BIZ_TIME <= TO_DATE('2010-12-31 59:59:59','yyyy-mm-dd hh24:mi:ss')"}]