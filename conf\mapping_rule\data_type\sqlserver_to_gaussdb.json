[{"sourceType": {"name": "int"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "tinyint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "smallint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "BINARY"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "varbinary"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "bit"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 8001, "lengthMax": 1000000}, "targetType": {"name": "text"}}, {"sourceType": {"name": "decimal"}, "targetType": {"name": "numeric"}}, {"sourceType": {"name": "real"}, "targetType": {"name": "numeric"}}, {"sourceType": {"name": "smallmoney"}, "targetType": {"name": "DECIMAL(10,4)"}}, {"sourceType": {"name": "money"}, "targetType": {"name": "DECIMAL(20,4)"}}, {"sourceType": {"name": "float"}, "targetType": {"name": "double precision"}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "uniqueidentifier"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(40)"}}, {"sourceType": {"name": "ntext"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "sysname"}, "targetType": {"name": "<PERSON><PERSON><PERSON>"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "xml"}, "targetType": {"name": "xml"}}]