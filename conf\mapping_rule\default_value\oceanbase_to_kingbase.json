[{}, null, {"minTargetDbVersion": "V8R6", "columnTypeIncludes": ["TIMESTAMP", "DATETIME"], "type": "RegexpReplace", "content": {"regexp": "[']current_timestamp[(][)][']", "replacement": "current_timestamp", "replaceType": "All"}}, {"minTargetDbVersion": "V8R6", "columnTypeIncludes": ["TIMESTAMP", "DATETIME"], "type": "RegexpReplace", "content": {"regexp": "[']current_timestamp[']", "replacement": "current_timestamp", "replaceType": "All"}}, {"minTargetDbVersion": "V8R6", "columnTypeIncludes": ["TIMESTAMP", "DATETIME", "TIME", "DATE"], "type": "RegexpReplace", "content": {"regexp": "[']curdate[(][)][']", "replacement": "current_date", "replaceType": "All"}}, {"minTargetDbVersion": "V8R6", "columnTypeIncludes": ["TIMESTAMP", "DATETIME", "TIME", "DATE"], "type": "RegexpReplace", "content": {"regexp": "[']now[(][)][']", "replacement": "current_timestamp", "replaceType": "All"}}, {"minTargetDbVersion": "V8R6", "columnTypeIncludes": ["TIMESTAMP", "DATETIME", "TIME", "DATE"], "type": "RegexpReplace", "content": {"regexp": "[']curtime[(][)][']", "replacement": "current_time", "replaceType": "All"}}, {"minTargetDbVersion": "V8R6", "columnTypeIncludes": ["VARCHAR"], "type": "RegexpReplace", "content": {"regexp": "\\x00", "replacement": "", "replaceType": "All"}}, {"columnTypeIncludes": ["BIT", "bit"], "type": "RegexpReplace", "content": {"regexp": "^0$", "replacement": "'0'", "replaceType": "All"}}, {"columnTypeIncludes": ["BIT", "bit"], "type": "RegexpReplace", "content": {"regexp": "^1$", "replacement": "'1'", "replaceType": "All"}}]