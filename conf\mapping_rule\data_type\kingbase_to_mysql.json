[{"sourceType": {"name": "GEOMETRY"}, "targetType": {"name": "GEOMETRY"}}, {"sourceType": {"name": "GEOMETRY"}, "targetType": {"name": "GEOMETRY"}, "regexMatchReplace": true, "regularExpressions": ["\"\\w+\"\\.\"GEOMETRY\""], "regularReplacements": ["GEOMETRY"]}, {"sourceType": {"name": "GEOGRAPHY"}, "targetType": {"name": "GEOMETRY"}}, {"sourceType": {"name": "GEOGRAPHY"}, "targetType": {"name": "GEOMETRY"}, "regexMatchReplace": true, "regularExpressions": ["\"\\w+\"\\.\"GEOGRAPHY\""], "regularReplacements": ["GEOMETRY"]}, {"sourceType": {"name": "BOX2D"}, "targetType": {"name": "GEOMETRY"}}, {"sourceType": {"name": "BOX2D"}, "targetType": {"name": "GEOMETRY"}, "regexMatchReplace": true, "regularExpressions": ["\"\\w+\"\\.\"BOX2D\""], "regularReplacements": ["GEOMETRY"]}, {"sourceType": {"name": "POINT"}, "targetType": {"name": "POINT"}}, {"sourceType": {"name": "POLYGON"}, "targetType": {"name": "POLYGON"}}, {"sourceType": {"name": "LSEG"}, "targetType": {"name": "LINESTRING"}}, {"sourceType": {"name": "PATH"}, "targetType": {"name": "GEOMETRY"}}, {"sourceType": {"name": "BOX"}, "targetType": {"name": "GEOMETRY"}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 2000}, "targetType": {"name": "text"}}, {"sourceType": {"name": "timestamp"}, "targetType": {"name": "datetime"}}, {"sourceType": {"name": "SMALLSERIAL", "autoIncrement": true}, "targetType": {"name": "TINYINT auto_increment"}}, {"sourceType": {"name": "SERIAL", "autoIncrement": true}, "targetType": {"name": "INT auto_increment"}}, {"sourceType": {"name": "BIGSERIAL", "autoIncrement": true}, "targetType": {"name": "BIGINT auto_increment"}}, {"sourceType": {"name": "datetime"}, "targetType": {"name": "datetime"}}]