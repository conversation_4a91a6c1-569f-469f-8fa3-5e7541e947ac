[{"sourceType": {"name": "XML"}, "targetType": {"name": "XML"}}, {"sourceType": {"name": "BOOLEAN"}, "targetType": {"name": "BOOLEAN"}}, {"sourceType": {"name": "BYTEA"}, "targetType": {"name": "BYTEA"}}, {"sourceType": {"name": "BIGINT"}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "INT8"}, "targetType": {"name": "INT8"}}, {"sourceType": {"name": "INT2"}, "targetType": {"name": "INT2"}}, {"sourceType": {"name": "SMALLINT"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "INT4"}, "targetType": {"name": "INT4"}}, {"sourceType": {"name": "_HPFLOAT"}, "targetType": {"name": "float4[]"}}, {"sourceType": {"name": "_INT4"}, "targetType": {"name": "INT4[]"}}, {"sourceType": {"name": "_BIT"}, "targetType": {"name": "bit[]"}}, {"sourceType": {"name": "BIT", "lengthMin": 1, "lengthMax": 64}, "targetType": {"name": "bit", "lengthMin": 1, "lengthMax": 64}}, {"sourceType": {"name": "_BOOLEAN"}, "targetType": {"name": "bool[]"}}, {"sourceType": {"name": "INTEGER"}, "targetType": {"name": "INTEGER"}}, {"maxTargetDbVersion": "V8R6", "sourceType": {"name": "INTERVALYTM"}, "targetType": {"name": "intervalym"}, "regexMatchReplace": true, "regularExpressions": ["INTERVALYTM(.*)", "INTERVAL(\\s*)YEAR(\\s*)TO(\\s*)MONTH"], "regularReplacements": ["interval year to month", "interval year to month"]}, {"minTargetDbVersion": "V8R6C7", "sourceType": {"name": "INTERVALYTM"}, "targetType": {"name": "intervalym"}, "regexMatchReplace": true, "regularExpressions": ["INTERVALYTM(.*)", "INTERVAL(\\s*)YEAR(\\s*)TO(\\s*)MONTH"], "regularReplacements": ["interval year(${PRECISION}) to month", "interval year(${PRECISION}) to month"]}, {"maxTargetDbVersion": "V8R6", "sourceType": {"name": "INTERVALDTS"}, "targetType": {"name": "INTERVALDS"}, "regexMatchReplace": true, "regularExpressions": ["INTERVALDTS(.*)", "INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND"], "regularReplacements": ["interval day to second(${SCALE})", "interval day to second", "interval day to second(${SCALE})", "interval day to second"]}, {"minTargetDbVersion": "V8R6C7", "sourceType": {"name": "INTERVALDTS"}, "targetType": {"name": "INTERVALDS"}, "regexMatchReplace": true, "regularExpressions": ["INTERVALDTS(.*)", "INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND"], "regularReplacements": ["interval day(${PRECISION}) to second(${SCALE})", "interval day(${PRECISION}) to second", "interval day(${PRECISION}) to second(${SCALE})", "interval day(${PRECISION}) to second"]}, {"sourceType": {"name": "text"}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "float4"}, "targetType": {"name": "REAL"}}, {"sourceType": {"name": "DOUBLE"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "double precision"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 10485760}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 10485760}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "DATE"}}, {"sourceType": {"name": "DATETIME"}, "targetType": {"name": "DATETIME"}}, {"sourceType": {"name": "TIME"}, "targetType": {"name": "TIME"}}, {"sourceType": {"name": "timetz"}, "targetType": {"name": "timetz"}}, {"sourceType": {"name": "TIME WITH TIME ZONE"}, "targetType": {"name": "TIME WITH TIME ZONE"}}, {"sourceType": {"name": "TIMESTAMP WITH TIME ZONE"}, "targetType": {"name": "TIMESTAMP WITH TIME ZONE"}}, {"sourceType": {"name": "BIT"}, "targetType": {"name": "bool"}}, {"sourceType": {"name": "BIT VARYING"}, "targetType": {"name": "BIT VARYING"}}, {"sourceType": {"name": "NUMERIC"}, "targetType": {"name": "NUMERIC"}}, {"sourceType": {"name": "TINYINT"}, "targetType": {"name": "TINYINT"}}, {"sourceType": {"name": "TINYINT"}, "targetType": {"name": "TINYINT"}}, {"sourceType": {"name": "TSQUERY"}, "targetType": {"name": "TSQUERY"}}, {"sourceType": {"name": "TSVECTOR"}, "targetType": {"name": "TSVECTOR"}}]