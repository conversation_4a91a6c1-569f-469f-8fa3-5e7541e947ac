AD<PERSON>, AFTER, AL<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ALLOW, ALTERAND, ANY, <PERSON><PERSON><PERSON>, ARRAY_EXISTS, AS,
AS<PERSON><PERSON><PERSON><PERSON>, ASSO<PERSON>AT<PERSON>, ASUTIME, AT, AUDIT, AUX, AUX<PERSON>IARY, BEFORE, BEGIN, <PERSON><PERSON>WEE<PERSON>,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BY, CALL, CAP<PERSON><PERSON>, CA<PERSON>AD<PERSON>, CASE, CAST, <PERSON><PERSON><PERSON>, CHAR, <PERSON><PERSON><PERSON><PERSON><PERSON>,
CH<PERSON><PERSON>, CLONE, CLOSE, <PERSON><PERSON><PERSON>TER, COLLECTION, COLL<PERSON>, COLUMN, COMMENT, COMMIT, CONCAT,
CONDITION, CONNECT, CONNECTION, CONSTRAINT, CONTAINS, CONTENT, CONTINUE, CREATE, CUBE, C<PERSON>RENT,
CURRENT_DATE, CURRENT_LC_CTYPE, CURRENT_PATH, CURRENT_SCHEMA, CURRENT_TIME, CURRENT_TIMESTAMP, CURRVAL, CURSOR, DATA, DATABASE,
DAY, DAYS, DBINFO, DECLARE, DEFA<PERSON>LT, DELETE, DESCRIP<PERSON><PERSON>, DE<PERSON>RMINIST<PERSON>, DISABLE, DISALLOW,
DIS<PERSON>NC<PERSON>, DO, D<PERSON><PERSON>ENT, DOUBLE, DROP, DSSIZE, DYNAMIC, EDITPROC, ELSE, ELSEIF,
ENCODING, ENCRYPTION, END, END-EXEC2, ENDING, ERASE, ESCAPE, EXCEPT, EXCEPTION, EXECUTE,
EXISTS, EXIT, EXPLAIN, EXTERNAL, FENCED, FETCH, FIELDPROC, FINAL, FIRST, FL 504 CURRENT_SERVER1,
FL 504 CURRENT_TIMEZONE1, FOR, FREE, FROM, FULL, FUNCTION, GENERATED, GET, GLOBAL, GO,
GOTO, GRANT, GROUP, HANDLER, HAVING, HOLD, HOUR, HOURS, IF, IMMEDIATE,
IN, INCLUSIVE, INDEX, INHERIT, INNER, INOUT, INSENSITIVE, INSERT, INTERSECT, INTO,
IS, ISOBID, ITERATE, JAR, JOIN, KEEP, KEY, LABEL, LANGUAGE, LAST,
LC_CTYPE, LEAVE, LEFT, LIKE, LIMIT1, LOCAL, LOCALE, LOCATOR, LOCATORS, LOCK,
LOCKMAX, LOCKSIZE, LONG, LOOP, MAINTAINED, MATERIALIZED, MICROSECOND, MICROSECONDS, MINUTEMINUTES, MODIFIES,
MONTH, MONTHS, NEXT, NEXTVAL, NO, NONE, NOT, NULL, NULLS, NUMPARTS,
OBID, OF, OFFSET1, OLD, ON, OPEN, OPTIMIZATION, OPTIMIZE, OR, ORDER,
ORGANIZATION, OUT, OUTER, PACKAGE, PADDED, PARAMETER, PART, PARTITION, PARTITIONED, PARTITIONING,
PATH, PERIOD, PIECESIZE, PLAN, PRECISION, PREPARE, PREVVAL, PRIOR, PRIQTY, PRIVILEGES,
PROCEDURE, PROGRAM, PSID, PUBLIC, QUERY, QUERYNO, READS, REFERENCES, REFRESH, RELEASE,
RENAME, REPEAT, RESIGNAL, RESTRICT, RESULT, RESULT_SET_LOCATOR, RETURN, RETURNS, REVOKE, RIGHT,
ROLE, ROLLBACK, ROLLUP, ROUND_CEILING, ROUND_DOWN, ROUND_FLOOR, ROUND_HALF_DOWN, ROUND_HALF_EVEN, ROUND_HALF_UP, ROUND_UP,
ROW, ROWSET, RUN, SAVEPOINT, SCHEMA, SCRATCHPAD, SECOND, SECONDS, SECQTY, SECURITY,
SELECT, SENSITIVE, SEQUENCE, SESSION_USER, SET, SIGNAL, SIMPLE, SOME, SOURCE, SPECIFIC,
STANDARD, STATEMENT, STATIC, STAY, STOGROUP, STORES, STYLE, SUMMARY, SYNONYM, SYSDATE,
SYSTEM, SYSTIMESTAMP, TABLE, TABLESPACE, THEN, TO, TRIGGER, TRUNCATE, TYPE, UNDO,
UNION, UNIQUE, UNTIL, UPDATE, USER, USING, VALIDPROC, VALUE, VALUES, VARIABLE,
VARIANT, VCAT, VERSIONING, VIEW, VOLATILE, VOLUMES, WHEN, WHENEVER, WHERE, WHILE,
WITH, WLM, XMLCAST, XMLEXISTS, XMLNAMESPACES, YEAR, YEARS, ZONE