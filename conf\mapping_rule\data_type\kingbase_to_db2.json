[{"sourceType": {"name": "MONEY"}, "targetType": {"name": "VARCHAR(100)"}}, {"sourceType": {"name": "FLOAT8"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "FLOAT4"}, "targetType": {"name": "DECFLOAT"}}, {"sourceType": {"name": "INT2"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "TINYINT"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "BIGINT"}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "OID"}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "TEXT"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "NCLOB"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "XML"}, "targetType": {"name": "XML"}}, {"sourceType": {"name": "BYTEA"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "VARBIT"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "BIT"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "TIME"}, "targetType": {"name": "TIME"}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "INT4"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "BIGSERIAL", "autoIncrement": true}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "INT8"}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "SMALLSERIAL"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "NAME"}, "targetType": {"name": "VARCHAR(64)"}}, {"sourceType": {"name": "TIME WITH TIME ZONE"}, "targetType": {"name": "TIME"}}, {"sourceType": {"name": "TIMETZ"}, "targetType": {"name": "TIME"}}, {"sourceType": {"name": "TIMESTAMP WITH TIMEZONE"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "TIMESTAMPTZ"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "ABSTIME"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "TIMESTAMP WITHOUT TIMEZONE"}, "targetType": {"name": "DATETIME"}}, {"sourceType": {"name": "CIDR"}, "targetType": {"name": "VARCHAR(100)"}}, {"sourceType": {"name": "BOOL"}, "targetType": {"name": "BOOLEAN"}}, {"sourceType": {"name": "BPCHAR", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "BPCHAR", "lengthMin": 256, "lengthMax": 99999}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "CHAR", "lengthMin": 256, "lengthMax": 99999}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 8001, "lengthMax": 99999}, "targetType": {"name": "DBCLOB"}}, {"sourceType": {"name": "POINT"}, "targetType": {"name": "VARCHAR(46)"}}, {"sourceType": {"name": "LINE"}, "targetType": {"name": "VARCHAR(30)"}}, {"sourceType": {"name": "POLYGON"}, "targetType": {"name": "VARCHAR(197)"}}, {"sourceType": {"name": "BOX"}, "targetType": {"name": "VARCHAR(97)"}}, {"sourceType": {"name": "CIRCLE"}, "targetType": {"name": "VARCHAR(73)"}}, {"sourceType": {"name": "LSEG"}, "targetType": {"name": "VARCHAR(100)"}}, {"sourceType": {"name": "PATH"}, "targetType": {"name": "VARCHAR(140)"}}, {"sourceType": {"name": "RASTER"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "GEOGRAPHY"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "GEOMETRY"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "JSON"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "MACADDR"}, "targetType": {"name": "VARCHAR(50)"}}, {"sourceType": {"name": "TSQUERY"}, "targetType": {"name": "VARCHAR(2046)"}}, {"sourceType": {"name": "TSVECTOR"}, "targetType": {"name": "VARCHAR(2046)"}}, {"sourceType": {"name": "UUID"}, "targetType": {"name": "VARCHAR(2046)"}}, {"sourceType": {"name": "_INT4"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 1, "precisionMax": 31, "scaleMin": 0, "scaleMax": 31}, "targetType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 31, "scaleMin": 0, "scaleMax": 31}}, {"sourceType": {"name": "NUMERIC"}, "targetType": {"name": "DECFLOAT"}}]