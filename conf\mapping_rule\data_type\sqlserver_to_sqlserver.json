[{"sourceType": {"name": "binary", "precisionMin": 1, "precisionMax": 8000}, "targetType": {"name": "binary", "precisionMin": 1, "precisionMax": 8000}}, {"sourceType": {"name": "image"}, "targetType": {"name": "image"}}, {"sourceType": {"name": "bit"}, "targetType": {"name": "bit"}}, {"sourceType": {"name": "char", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 8001, "lengthMax": 2147483647}, "targetType": {"name": "varchar(max)"}}, {"sourceType": {"name": "bigint", "autoIncrement": true}, "targetType": {"name": "bigint identity"}}, {"sourceType": {"name": "decimal", "autoIncrement": true}, "targetType": {"name": "decimal identity"}}, {"sourceType": {"name": "numeric", "autoIncrement": true}, "targetType": {"name": "numeric identity"}}, {"sourceType": {"name": "bigint"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "int", "autoIncrement": true}, "targetType": {"name": "int identity"}}, {"sourceType": {"name": "int"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "smallint", "autoIncrement": true}, "targetType": {"name": "smallint identity"}}, {"sourceType": {"name": "smallint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "tinyint", "autoIncrement": true}, "targetType": {"name": "tinyint identity"}}, {"sourceType": {"name": "tinyint"}, "targetType": {"name": "tinyint"}}, {"sourceType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "decimal", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "decimal", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "float"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "double"}, "targetType": {"name": "double"}}, {"sourceType": {"name": "money"}, "targetType": {"name": "money"}}, {"sourceType": {"name": "smallmoney"}, "targetType": {"name": "smallmoney"}}, {"sourceType": {"name": "nchar", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "nchar", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "ntext"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 8001, "lengthMax": 2147483647}, "targetType": {"name": "nvar<PERSON><PERSON>(max)"}}, {"sourceType": {"name": "real"}, "targetType": {"name": "real"}}, {"sourceType": {"name": "sysname"}, "targetType": {"name": "sysname"}}, {"sourceType": {"name": "text"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "timestamp"}, "targetType": {"name": "timestamp"}}, {"sourceType": {"name": "uniqueidentifier"}, "targetType": {"name": "uniqueidentifier"}}, {"sourceType": {"name": "geometry"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "geography"}, "targetType": {"name": "geography"}}, {"sourceType": {"name": "varbinary", "precisionMin": 1, "precisionMax": 8000}, "targetType": {"name": "varbinary", "precisionMin": 1, "precisionMax": 8000}}, {"sourceType": {"name": "varbinary", "precisionMin": 8001, "precisionMax": 2147483647}, "targetType": {"name": "varbinary(max)"}}, {"sourceType": {"name": "datetime", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "datetime", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "datetime"}, "targetType": {"name": "datetime"}}, {"sourceType": {"name": "smalldatetime", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "smalldatetime", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "smalldatetime"}, "targetType": {"name": "smalldatetime"}}, {"sourceType": {"name": "date"}, "targetType": {"name": "date"}}, {"sourceType": {"name": "datetime2", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "datetime2", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "datetime2"}, "targetType": {"name": "datetime2"}}, {"sourceType": {"name": "datetimeoffset", "scaleMin": 1, "scaleMax": 7}, "targetType": {"name": "datetimeoffset", "scaleMin": 1, "scaleMax": 7}}, {"sourceType": {"name": "datetimeoffset"}, "targetType": {"name": "datetimeoffset"}}, {"sourceType": {"name": "time"}, "targetType": {"name": "time"}, "regexMatchReplace": true, "regularExpressions": ["TIME(\\s*)"], "regularReplacements": ["time(${SCALE})"]}, {"sourceType": {"name": "xml"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 1}, "targetType": {"name": "numeric", "precisionMin": 1}, "regexMatchReplace": true, "regularExpressions": ["NUMERIC(.*)"], "regularReplacements": ["NUMERIC(${PRECISION},${SCALE})"]}]