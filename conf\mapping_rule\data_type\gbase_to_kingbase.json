[{"sourceType": {"name": "SERIAL8"}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "SMALLFLOAT"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "NCHAR", "lengthMin": 1, "lengthMax": 32767, "charUsedSupport": true}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 32767}}, {"sourceType": {"name": "NVARCHAR", "lengthMin": 1, "lengthMax": 32767, "charUsedSupport": true}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 32767}}, {"sourceType": {"name": "LVARCHAR"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "TEXT"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 0}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 0, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "numeric", "precisionMin": 38, "precisionMax": 38, "scaleMin": 0, "scaleMax": 0}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 0, "scaleMin": -127, "scaleMax": -127}, "targetType": {"name": "numeric"}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 38, "scaleMin": -84, "scaleMax": -1}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": -84, "scaleMax": -1, "attachNegativeScaleSize": false}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 38, "scaleMin": 1, "scaleMax": 127}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 1, "scaleMax": 127}}, {"sourceType": {"name": "XMLType"}, "targetType": {"name": "XML"}}]