[{"sourceType": {"name": "GRAPHIC", "lengthMin": 1, "lengthMax": 128}, "targetType": {"name": "GRAPHIC", "lengthMin": 1, "lengthMax": 128}}, {"sourceType": {"name": "DECFLOAT"}, "targetType": {"name": "DECFLOAT"}}, {"sourceType": {"name": "VARGRAPHIC", "lengthMin": 1, "lengthMax": 16336}, "targetType": {"name": "VARGRAPHIC", "lengthMin": 1, "lengthMax": 16336}}, {"sourceType": {"name": "VARGRAPHIC", "lengthMin": 16336, "lengthMax": 32672}, "targetType": {"name": "VARGRAPHIC", "lengthMin": 16336, "lengthMax": 16336}}, {"sourceType": {"name": "DBCLOB"}, "targetType": {"name": "DBCLOB"}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 38}, "targetType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38}}, {"sourceType": {"name": "ENUM"}, "targetType": {"name": "CHAR"}}, {"sourceType": {"name": "SET"}, "targetType": {"name": "CHAR"}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "DATE"}}, {"sourceType": {"name": "INTEGER"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "SMALLINT"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "BIGINT"}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "XML"}, "targetType": {"name": "XML"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "DATETIME"}}, {"sourceType": {"name": "VARBINARY", "precisionMin": 1, "precisionMax": 32672}, "targetType": {"name": "VARBINARY", "precisionMin": 1, "precisionMax": 32672}}, {"sourceType": {"name": "BOOLEAN"}, "targetType": {"name": "BOOLEAN"}}, {"sourceType": {"name": "BINARY", "precisionMin": 1, "precisionMax": 255}, "targetType": {"name": "BINARY", "precisionMin": 1, "precisionMax": 255}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 32704}, "targetType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 32704}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "LONG VARCHAR FOR BIT DATA"}, "targetType": {"name": "VARBINARY"}}, {"sourceType": {"name": "VARCHAR FOR BIT DATA"}, "targetType": {"name": "VARBINARY"}}, {"sourceType": {"name": "REAL"}, "targetType": {"name": "REAL"}}, {"sourceType": {"name": "TIME"}, "targetType": {"name": "TIME"}}, {"sourceType": {"name": "DOUBLE"}, "targetType": {"name": "DOUBLE"}}]