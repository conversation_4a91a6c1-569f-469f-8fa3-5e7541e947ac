[{}, null, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "(?i)newid", "replacement": "sys_guid", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(\\s*(\\d+)\\s*\\)\\s*\\)\\)?", "replacement": "localdatetime", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\s*\\(\\s*\\(\\s*(\\s*\\d+\\s*)\\s*\\)\\s*\\)", "replacement": "$1", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "replicate", "replacement": "repeat", "replaceType": "All"}}]