[{"sourceType": {"name": "GRAPHIC", "lengthMin": 1, "lengthMax": 128}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 128}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "REAL"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "VARCHAR"}, "targetType": {"name": "VARCHAR"}}, {"sourceType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000, "charUsedSupport": true}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "NVARCHAR2", "lengthMin": 1, "lengthMax": 65535}, "targetType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 65535}}, {"sourceType": {"name": "NVARCHAR2", "lengthMin": 65536, "lengthMax": 2147483647}, "targetType": {"name": "string"}}, {"sourceType": {"name": "LONG"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "XMLTYPE"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "BFILE"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "CHAR", "lengthMin": 256, "lengthMax": 9999}, "targetType": {"name": "char"}}, {"sourceType": {"name": "NCHAR", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "CHAR", "lengthExpressions": "length * 2"}}, {"sourceType": {"name": "NCHAR", "lengthMin": 256, "lengthMax": 9999}, "targetType": {"name": "string"}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "LONG RAW"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "TIMESTAMP WITH TIMEZONE"}, "targetType": {"name": "timestamp"}}, {"sourceType": {"name": "TIMESTAMP WITHOUT TIMEZONE"}, "targetType": {"name": "timestamp"}}, {"sourceType": {"name": "TIMESTAMP WITH LOCAL TIMEZONE"}, "targetType": {"name": "timestamp"}}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "timestamp"}}, {"sourceType": {"name": "RAW"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "BINARY_FLOAT"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "BINARY_DOUBLE"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "ROWID"}, "targetType": {"name": "CHAR(18)"}}, {"sourceType": {"name": "NCLOB"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "UROWID", "lengthMin": 1, "lengthMax": 99999}, "targetType": {"name": "string"}}, {"sourceType": {"name": "INTERVAL YEAR"}, "targetType": {"name": "CHAR(12)"}}, {"sourceType": {"name": "INTERVAL DAY"}, "targetType": {"name": "CHAR(25)"}}, {"sourceType": {"name": "INTERVALDS"}, "targetType": {"name": "CHAR(25)"}}, {"sourceType": {"name": "INTERVALYM"}, "targetType": {"name": "CHAR(25)"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127, "matchExpressions": "precision < scale"}, "targetType": {"name": "DECIMAL(38, 0)"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127}, "targetType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127}}, {"sourceType": {"name": "NUMBER"}, "targetType": {"name": "DECIMAL(38, 0)"}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127, "matchExpressions": "precision < scale"}, "targetType": {"name": "DECIMAL(38, 38)"}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127}, "targetType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127, "matchExpressions": "precision < scale"}, "targetType": {"name": "DECIMAL(38, 38)"}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127}, "targetType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127}}, {"sourceType": {"name": "DECIMAL"}, "targetType": {"name": "DECIMAL(38, 38)"}}]