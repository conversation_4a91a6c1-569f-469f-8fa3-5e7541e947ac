[{"sourceType": {"name": "xml"}, "targetType": {"name": "xml"}}, {"sourceType": {"name": "bigint"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "bigint", "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "binary"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "bit"}, "targetType": {"name": "boolean"}}, {"sourceType": {"name": "char", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "datetime", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "timestamp", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "smalldatetime", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "timestamp", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "decimal", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "decimal", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "decimal", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38, "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "float"}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "double"}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "image"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "int"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "int", "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "smallint", "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "money", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "smallmoney", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "nchar", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "ntext"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38, "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 8001, "lengthMax": 2147483647}, "targetType": {"name": "text"}}, {"sourceType": {"name": "real"}, "targetType": {"name": "real"}}, {"sourceType": {"name": "univarchar"}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "unichar"}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "unitext"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "double precision"}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "smallint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "sysname", "lengthMin": 1, "lengthMax": 2147483647}, "targetType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 2147483647}}, {"sourceType": {"name": "text"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "timestamp"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "tinyint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "tinyint", "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "uniqueidentifier", "lengthMin": 1, "lengthMax": 14085760}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 14085760}}, {"sourceType": {"name": "varbinary"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 8001, "lengthMax": 2147483647}, "targetType": {"name": "text"}}, {"sourceType": {"name": "date"}, "targetType": {"name": "date"}}, {"sourceType": {"name": "bigdatetime"}, "targetType": {"name": "timestamp"}}, {"sourceType": {"name": "bigtime"}, "targetType": {"name": "timestamp"}}, {"sourceType": {"name": "time"}, "targetType": {"name": "time"}}]