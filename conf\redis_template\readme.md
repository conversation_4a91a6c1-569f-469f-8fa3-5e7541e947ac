此目录是“目标端支持redis的模板”存放目录，在迁移过程中如果目标端为redis，需要制定生成redis数据的模板，
按约定命名存放，再次迁移时程序将直接读取手工脚本。

**注意：**

（1）redis模板执行生效，前置条件是修改配置文件datasource-kingbase_to_redis.yml，设置参数dmlTemplatePath: simple

（2）为了避免大小写敏感影响模板的读取，文件夹名一律与源库对象名大小写保持一致。

1. 目录命名规则
   redis_template
   └─simple
     └─schema1
       └─table_name.json
     └─schemaname
       └─table_name.json
   在redis_template目录下：
   第一级目录是“自定义任务名（英文字母组成）”，如上面的simple；
   第二级目录是“需要迁移的schema，可以多个schema放在同级目录下”，如上面的schema1、schemaname；
   第三级目录是“表名.json”，如上面的table_name.json；
