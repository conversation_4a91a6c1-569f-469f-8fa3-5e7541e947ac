[{}, null, {"columnTypeIncludes": ["TIMESTAMP", "timestamp", "VARCHAR", "<PERSON><PERSON><PERSON>"], "type": "RegexpReplace", "content": {"regexp": "\"sysdate\"", "replacement": "current_timestamp", "replaceType": "All"}}, {"columnTypeIncludes": ["VARCHAR", "<PERSON><PERSON><PERSON>"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+)::varchar", "replacement": "${defaultValue}", "replaceType": "All"}}, {"columnTypeIncludes": ["CHAR", "char", "BPCHAR", "bpchar"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+)::bpchar", "replacement": "${defaultValue}", "replaceType": "All"}}, {"columnTypeIncludes": ["TIMESTAMP", "timestamp"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+)::timestamp without time zone", "replacement": "${defaultValue}", "replaceType": "All"}}, {"columnTypeIncludes": ["NUMERIC", "numeric", "DECIMAL", "decimal"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+)::integer", "replacement": "${defaultValue}", "replaceType": "All"}}, {"columnTypeIncludes": ["NUMERIC", "numeric", "DECIMAL", "decimal"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+)::numeric", "replacement": "${defaultValue}", "replaceType": "All"}}]