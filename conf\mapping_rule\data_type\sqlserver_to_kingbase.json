[{"sourceType": {"name": "binary"}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "image"}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "bit"}, "targetType": {"name": "bit"}}, {"sourceType": {"name": "char", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 8001, "lengthMax": 2147483647}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "bigint", "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "bigint"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "int", "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "int"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "smallint", "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "smallint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "tinyint", "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "tinyint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "numeric", "precisionMin": 1, "precisionMax": 4, "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "numeric", "precisionMin": 5, "precisionMax": 9, "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "numeric", "precisionMin": 10, "precisionMax": 38, "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "numeric", "precisionMin": 18, "precisionMax": 18, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "decimal", "precisionMin": 1, "precisionMax": 4, "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "decimal", "precisionMin": 5, "precisionMax": 9, "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "decimal", "precisionMin": 10, "precisionMax": 38, "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "decimal", "precisionMin": 18, "precisionMax": 18, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "decimal", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "decimal", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "float"}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "double"}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "money"}, "targetType": {"name": "money"}}, {"sourceType": {"name": "smallmoney"}, "targetType": {"name": "money"}}, {"sourceType": {"name": "nchar", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "ntext"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "text"}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 8001, "lengthMax": 2147483647}, "targetType": {"name": "text"}}, {"sourceType": {"name": "real"}, "targetType": {"name": "real"}}, {"sourceType": {"name": "sysname", "lengthMin": 1, "lengthMax": 2147483647}, "targetType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 2147483647}}, {"sourceType": {"name": "text"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "image"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "timestamp"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "uniqueidentifier", "lengthMin": 1, "lengthMax": 14085760}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 14085760}}, {"sourceType": {"name": "geometry"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "geography"}, "targetType": {"name": "geography"}}, {"sourceType": {"name": "varbinary"}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "datetime", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "timestamp", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "smalldatetime", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "timestamp", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "date"}, "targetType": {"name": "date"}}, {"sourceType": {"name": "datetime2", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "timestamp", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "datetimeoffset", "scaleMin": 1, "scaleMax": 7}, "targetType": {"name": "timestamp with time zone", "scaleMin": 1, "scaleMax": 6}}, {"sourceType": {"name": "time"}, "targetType": {"name": "time"}, "regexMatchReplace": true, "regularExpressions": ["TIME(\\s*)"], "regularReplacements": ["time(${SCALE})"]}, {"sourceType": {"name": "xml"}, "targetType": {"name": "xml"}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 1}, "targetType": {"name": "numeric", "precisionMin": 1}, "regexMatchReplace": true, "regularExpressions": ["NUMERIC(.*)"], "regularReplacements": ["NUMERIC(${PRECISION},${SCALE})"]}]