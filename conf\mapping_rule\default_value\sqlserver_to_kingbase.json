[{}, null, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "(?i)newid", "replacement": "sys_guid", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\(?CONVERT\\s*\\(\\s*\\[?\\s*varchar\\s*\\]?\\s*\\(\\s*(\\d+)\\s*\\)\\s*,\\s*getdate\\s*\\(\\s*\\)\\s*,\\s*\\(\\s*(\\d+)\\s*\\)\\s*\\)\\)?", "replacement": "localdatetime", "replaceType": "All"}}, {"columnTypeIncludes": ["bit"], "type": "RegexpReplace", "content": {"regexp": "0", "replacement": "b'0'", "replaceType": "All"}}, {"columnTypeIncludes": ["bit"], "type": "RegexpReplace", "content": {"regexp": "1", "replacement": "b'1'", "replaceType": "All"}}, {"columnTypeIncludes": ["datetime", "timestamp"], "type": "RegexpReplace", "content": {"regexp": "^0$", "replacement": "1900-01-01 00:00:00.000", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\s*\\(\\s*\\(\\s*(\\s*\\d+\\s*)\\s*\\)\\s*\\)", "replacement": "$1", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "replicate", "replacement": "repeat", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\(NEXT\\s+VALUE\\s+FOR\\s+\\[(?<defaultValue>\\S+)\\]\\)", "replacement": "nextval('${defaultValue}')", "replaceType": "All"}}]