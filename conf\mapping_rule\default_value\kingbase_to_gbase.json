[{}, null, {"columnTypeIncludes": ["CHAR", "char", "BPCHAR", "bpchar"], "type": "RegexpReplace", "content": {"regexp": "(?<defaultValue>\\S+)::bpchar", "replacement": "${defaultValue}", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\'(?<defaultValue>\\S+)\\'::integer", "replacement": "${defaultValue}::integer", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "replace\\(to_char\\(\\(\"sysdate\"\\(\\)\\)::timestamp without time zone,\\s+\\'yyyy-mm-dd hh24:mi:ss'::text\\),\\s+\\'\\s+\\'::text,\\s+\\'T\\'::text\\)", "replacement": "to_char(SY<PERSON><PERSON><PERSON> YEAR TO SECOND,'YYYY-MM-DD HH24:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "replace\\(to_char\\(\\(\"sysdate\"\\(\\)\\)::timestamp without time zone,\\s+\\'yyyy-mm-dd hh24:mi:ss'::text\\),\\s+NULL::text,\\s+\\'T\\'::text\\)", "replacement": "to_char(SY<PERSON><PERSON><PERSON> YEAR TO SECOND,'YYYY-MM-DD HH24:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "replace\\(to_char\\(\\(CURRENT_TIMESTAMP\\)::timestamp without time zone,\\s+\\'yyyy-mm-dd hh24:mi:ss'::text\\),\\s+\\'\\s+\\'::text,\\s+\\'T\\'::text\\)", "replacement": "to_char(SY<PERSON><PERSON><PERSON> YEAR TO SECOND,'YYYY-MM-DD HH24:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "replace\\(to_char\\(\\(CURRENT_TIMESTAMP\\)::timestamp without time zone,\\s+\\'yyyy-mm-dd hh24:mi:ss'::text\\),\\s+NULL::text,\\s+\\'T\\'::text\\)", "replacement": "to_char(SY<PERSON><PERSON><PERSON> YEAR TO SECOND,'YYYY-MM-DD HH24:MI:SS')", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "CURRENT_TIMESTAMP", "replacement": "SYSDATE YEAR TO SECOND", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\"sysdate\"\\(\\)", "replacement": "SYSDATE YEAR TO SECOND", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "to_date\\('1970-01-01'::text, 'yyyy-MM-dd'::text\\)", "replacement": "\"1970-01-01 00:00:00\"", "replaceType": "All"}}]