[{"minSourceDbVersion": "V7", "sourceType": {"name": "BIT", "lengthMin": 1, "lengthMax": 64000}, "targetType": {"name": "BIT", "lengthMin": 1, "lengthMax": 64000}}, {"minSourceDbVersion": "V7", "sourceType": {"name": "BIT VARYING", "lengthMin": 1, "lengthMax": 64000}, "targetType": {"name": "BIT VARYING", "lengthMin": 1, "lengthMax": 64000}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "BYTEA"}, "targetType": {"name": "BYTEA"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "BLOB"}, "targetType": {"name": "BLOB"}}, {"minSourceDbVersion": "V7", "sourceType": {"name": "CHARACTER", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "CHARACTER", "lengthMin": 1, "lengthMax": 8000}}, {"minSourceDbVersion": "V7", "sourceType": {"name": "CHARACTER VARYING", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "CHARACTER VARYING", "lengthMin": 1, "lengthMax": 8000}}, {"minSourceDbVersion": "V7", "sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 8000}}, {"minSourceDbVersion": "V7", "sourceType": {"name": "CHAR  VARYING", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "CHAR  VARYING", "lengthMin": 1, "lengthMax": 8000}}, {"minSourceDbVersion": "V7", "sourceType": {"name": "CHAR  VARYING", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "CHAR  VARYING", "lengthMin": 1, "lengthMax": 8000}}, {"minSourceDbVersion": "V7", "sourceType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 8000}}, {"minSourceDbVersion": "V7", "sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 8000, "charUsedSupport": true}, "targetType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 8000}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "CLOB"}, "targetType": {"name": "CLOB"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "TEXT"}, "targetType": {"name": "TEXT"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "BIGINT", "autoIncrement": true}, "targetType": {"name": "BIGSERIAL"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "BIGINT"}, "targetType": {"name": "BIGINT"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "TINYINT"}, "targetType": {"name": "TINYINT"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "SMALLINT"}, "targetType": {"name": "SMALLINT"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "INTEGER", "autoIncrement": true}, "targetType": {"name": "SERIAL"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "smallint", "autoIncrement": true}, "targetType": {"name": "SERIAL"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "tinyint", "autoIncrement": true}, "targetType": {"name": "SERIAL"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "INTEGER"}, "targetType": {"name": "INTEGER"}}, {"minSourceDbVersion": "V7", "sourceType": {"name": "NUMERIC", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "NUMERIC", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"minSourceDbVersion": "V7", "sourceType": {"name": "FLOAT", "lengthMin": 1, "lengthMax": 53}, "targetType": {"name": "FLOAT", "lengthMin": 1, "lengthMax": 53}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "REAL"}, "targetType": {"name": "REAL"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "DOUBLE PRECISION"}, "targetType": {"name": "DOUBLE PRECISION"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "DATE"}, "targetType": {"name": "DATE"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "TIME"}, "targetType": {"name": "TIME"}}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "TIMESTAMP WITH TIME ZONE"}, "targetType": {"name": "TIMESTAMP WITH TIME ZONE"}}, {"sourceType": {"name": "TIMESTAMPTZ"}, "targetType": {"name": "TIMESTAMPTZ"}, "regexMatchReplace": true, "regularExpressions": ["TIMESTAMPTZ(.*)", "TIMESTAMP(.*)WITH TIME ZONE"], "regularReplacements": ["TIMESTAMPTZ(${SCALE})", "TIMESTAMPTZ(${SCALE})"]}, {"maxSourceDbVersion": "V7", "sourceType": {"name": "BOOLEAN"}, "targetType": {"name": "BOOLEAN"}}]