[{"sourceType": {"name": "GRAPHIC", "lengthMin": 1, "lengthMax": 128}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 128}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "DATETIME"}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "REAL"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "VARCHAR"}, "targetType": {"name": "VARCHAR"}}, {"sourceType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000, "charUsedSupport": true}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "NVARCHAR2", "lengthMin": 1, "lengthMax": 65535}, "targetType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 65535}}, {"sourceType": {"name": "NVARCHAR2", "lengthMin": 65536, "lengthMax": 2147483647}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "LONG"}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "XMLTYPE"}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "BFILE"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255, "charUsedSupport": false}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "CHAR", "lengthMin": 256, "lengthMax": 9999, "charUsedSupport": false}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "NCHAR", "lengthMin": 1, "lengthMax": 255, "charUsedSupport": false}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "NCHAR", "lengthMin": 256, "lengthMax": 9999, "charUsedSupport": false}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "LONG RAW"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "TIMESTAMP WITH TIMEZONE"}, "targetType": {"name": "DATETIME"}}, {"sourceType": {"name": "TIMESTAMP WITHOUT TIMEZONE"}, "targetType": {"name": "DATETIME"}}, {"sourceType": {"name": "TIMESTAMP WITH LOCAL TIMEZONE"}, "targetType": {"name": "DATETIME"}}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "DATETIME"}}, {"sourceType": {"name": "RAW"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "BINARY_FLOAT"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "BINARY_DOUBLE"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "ROWID"}, "targetType": {"name": "CHAR(18)"}}, {"sourceType": {"name": "NCLOB"}, "targetType": {"name": "LONGTEXT"}}, {"sourceType": {"name": "UROWID", "lengthMin": 1, "lengthMax": 99999}, "targetType": {"name": "text"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 1, "scaleMax": 0}, "targetType": {"name": "BIT"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 3, "precisionMax": 3, "scaleMax": 0}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 5, "precisionMax": 5, "scaleMax": 0}, "targetType": {"name": "MEDIUMINT"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 7, "precisionMax": 7, "scaleMax": 0}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 10, "precisionMax": 10, "scaleMax": 0}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 19, "precisionMax": 19, "scaleMax": 0}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "INTERVAL YEAR"}, "targetType": {"name": "CHAR(12)"}}, {"sourceType": {"name": "INTERVAL DAY"}, "targetType": {"name": "CHAR(25)"}}, {"sourceType": {"name": "INTERVALDS"}, "targetType": {"name": "CHAR(25)"}}, {"sourceType": {"name": "INTERVALYM"}, "targetType": {"name": "CHAR(25)"}}]