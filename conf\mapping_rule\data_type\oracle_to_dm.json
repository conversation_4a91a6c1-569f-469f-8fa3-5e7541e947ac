[{"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 2000, "charUsedSupport": true}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000, "charUsedSupport": true}, "targetType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "NVARCHAR2", "lengthMin": 1, "lengthMax": 2000, "charUsedSupport": true}, "targetType": {"name": "NVARCHAR2", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "NCLOB"}, "targetType": {"name": "NCLOB"}}, {"sourceType": {"name": "binary_float"}, "targetType": {"name": "double"}}, {"sourceType": {"name": "binary_double"}, "targetType": {"name": "double"}}, {"sourceType": {"name": "xmltype"}, "targetType": {"name": "xmltype"}}, {"sourceType": {"name": "RAW", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "raw", "lengthMin": 1, "lengthMax": 2000}}]