[{"sourceType": {"name": "TINYINT"}, "targetType": {"name": "tinyint"}}, {"sourceType": {"name": "tinyint unsigned"}, "targetType": {"name": "tinyint unsigned"}}, {"sourceType": {"name": "SMALLINT", "autoIncrement": true}, "targetType": {"name": "smallint auto_increment"}}, {"sourceType": {"name": "SMALLINT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "int auto_increment"}}, {"sourceType": {"name": "int UNSIGNED"}, "targetType": {"name": "int unsigned"}}, {"sourceType": {"name": "SMALLINT UNSIGNED"}, "targetType": {"name": "int unsigned"}}, {"sourceType": {"name": "TINYINT", "autoIncrement": true}, "targetType": {"name": "tinyint auto_increment"}}, {"sourceType": {"name": "INT", "autoIncrement": true}, "targetType": {"name": "int auto_increment"}}, {"sourceType": {"name": "INT UNSIGNED"}, "targetType": {"name": "int unsigned"}}, {"sourceType": {"name": "INTEGER UNSIGNED"}, "targetType": {"name": "integer unsigned"}}, {"sourceType": {"name": "INT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "bigint auto_increment"}}, {"sourceType": {"name": "MEDIUMINT"}, "targetType": {"name": "mediumint"}}, {"sourceType": {"name": "MEDIUMINT", "autoIncrement": true}, "targetType": {"name": "mediumint auto_increment"}}, {"sourceType": {"name": "MEDIUMINT UNSIGNED"}, "targetType": {"name": "int unsigned"}}, {"sourceType": {"name": "MEDIUMINT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "mediumint auto_increment"}}, {"sourceType": {"name": "BIGINT", "autoIncrement": true}, "targetType": {"name": "bigint auto_increment"}}, {"sourceType": {"name": "BIGINT UNSIGNED"}, "targetType": {"name": "bigint unsigned"}}, {"sourceType": {"name": "BIGINT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "bigint unsigned auto_increment"}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 0, "lengthMax": 0}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 1}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 65537}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 65537}}, {"sourceType": {"name": "TINYTEXT"}, "targetType": {"name": "tinytext"}}, {"sourceType": {"name": "MEDIUMTEXT"}, "targetType": {"name": "mediumtext"}}, {"sourceType": {"name": "LONGTEXT"}, "targetType": {"name": "longtext"}}, {"sourceType": {"name": "TINYBLOB"}, "targetType": {"name": "tinyblob"}}, {"sourceType": {"name": "MEDIUMBLOB"}, "targetType": {"name": "mediumblob"}}, {"sourceType": {"name": "LONGBLOB"}, "targetType": {"name": "longblob"}}, {"sourceType": {"name": "BINARY", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "binary", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "VARBINARY", "lengthMin": 1, "lengthMax": 65532}, "targetType": {"name": "varbinary", "lengthMin": 1, "lengthMax": 65532}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "BINARY", "lengthMin": 0, "lengthMax": 255}, "targetType": {"name": "binary", "lengthMin": 0, "lengthMax": 255}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "VARBINARY", "lengthMin": 0, "lengthMax": 65532}, "targetType": {"name": "varbinary", "lengthMin": 0, "lengthMax": 65532}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "date"}}, {"sourceType": {"name": "DATETIME", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "DATETIME", "lengthMin": 1, "lengthMax": 6}}, {"sourceType": {"name": "YEAR"}, "targetType": {"name": "year"}}, {"sourceType": {"name": "JSON"}, "targetType": {"name": "json"}}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "timestamp"}, "regexMatchReplace": true, "regularExpressions": ["timestamp(.*)"], "regularReplacements": ["timestamp(${scale}) without time zone"]}]