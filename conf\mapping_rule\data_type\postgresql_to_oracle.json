[{"sourceType": {"name": "DOUBLE"}, "targetType": {"name": "NUMBER"}}, {"sourceType": {"name": "INT2"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "TINYINT"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "INT8"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "INT4"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "OID"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "REG"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "TEXT"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "BOOL"}, "targetType": {"name": "VARCHAR2(10)"}}, {"sourceType": {"name": "BYTEA"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "BIT", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "RAW", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "BIT", "matchExpressions": "length > 2000"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "VARBIT", "matchExpressions": "length > 2000"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "VARBIT", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "RAW", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "XML"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "INET"}, "targetType": {"name": "VARCHAR2(30)"}}, {"sourceType": {"name": "TIME"}, "targetType": {"name": "VARCHAR2(18)"}}, {"sourceType": {"name": "TIMETZ"}, "targetType": {"name": "VARCHAR2(18)"}}, {"sourceType": {"name": "TIME WITH TIME ZONE"}, "targetType": {"name": "VARCHAR2(18)"}}, {"sourceType": {"name": "CIDR"}, "targetType": {"name": "VARCHAR2(20)"}}, {"sourceType": {"name": "TIMESTAMP WITHOUT TIMEZONE"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "TIMESTAMPTZ"}, "targetType": {"name": "TIMESTAMP WITH TIME ZONE"}}, {"sourceType": {"name": "TIMESTAMP WITH TIMEZONE"}, "targetType": {"name": "TIMESTAMP WITH TIME ZONE"}}, {"sourceType": {"name": "INTERVAL"}, "targetType": {"name": "VARCHAR2(50)"}}, {"sourceType": {"name": "TINTERVAL"}, "targetType": {"name": "VARCHAR2(50)"}}, {"sourceType": {"name": "SERIAL"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "INT2"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "BIGSERIAL"}, "targetType": {"name": "NUMBER(20)"}}, {"sourceType": {"name": "SMALLSERIAL"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "MONEY"}, "targetType": {"name": "NUMERIC(38,2)"}}, {"sourceType": {"name": "BPCHAR", "matchExpressions": "length * 3 > 2000"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "BPCHAR", "matchExpressions": "length * 3 <= 2000"}, "targetType": {"name": "CHAR", "lengthExpressions": "length * 3"}}, {"sourceType": {"name": "CHAR", "matchExpressions": "length * 3 > 2000"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "CHAR", "matchExpressions": "length * 3 <= 2000"}, "targetType": {"name": "CHAR", "lengthExpressions": "length * 3"}}, {"sourceType": {"name": "VARCHAR", "matchExpressions": "length * 3 > 4000"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "VARCHAR", "matchExpressions": "length * 3 <= 4000"}, "targetType": {"name": "VARCHAR2", "lengthExpressions": "length * 3"}}, {"sourceType": {"name": "NVARCHAR", "matchExpressions": "length * 3 > 4000"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "NVARCHAR", "matchExpressions": "length * 3 <= 4000"}, "targetType": {"name": "VARCHAR2", "lengthExpressions": "length * 3"}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "POINT"}, "targetType": {"name": "VARCHAR(46)"}}, {"sourceType": {"name": "LINE"}, "targetType": {"name": "VARCHAR(30)"}}, {"sourceType": {"name": "POLYGON"}, "targetType": {"name": "VARCHAR(197)"}}, {"sourceType": {"name": "BOX"}, "targetType": {"name": "VARCHAR(97)"}}, {"sourceType": {"name": "CIRCLE"}, "targetType": {"name": "VARCHAR(73)"}}, {"sourceType": {"name": "LSEG"}, "targetType": {"name": "VARCHAR(100)"}}, {"sourceType": {"name": "PATH"}, "targetType": {"name": "VARCHAR(140)"}}, {"sourceType": {"name": "RASTER"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "GEOGRAPHY"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "GEOMETRY"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "JSON"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "MACADDR"}, "targetType": {"name": "VARCHAR2(50)"}}, {"sourceType": {"name": "TSQUERY"}, "targetType": {"name": "VARCHAR2(2046)"}}, {"sourceType": {"name": "TSVECTOR"}, "targetType": {"name": "VARCHAR2(2046)"}}, {"sourceType": {"name": "UUID"}, "targetType": {"name": "VARCHAR2(2046)"}}, {"sourceType": {"name": "ABSTIME"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "RELTIME"}, "targetType": {"name": "VARCHAR2(50)"}}]