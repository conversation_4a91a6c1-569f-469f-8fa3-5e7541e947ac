[{"sourceType": {"name": "BIGINT"}, "targetType": {"name": "NUMBER(20, 0)"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "BINARY"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "VARBINARY"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "GRAPHIC", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "DATE"}}, {"sourceType": {"name": "TIMESTAMP", "precisionMin": 1, "precisionMax": 6}, "targetType": {"name": "TIMESTAMP WITH TIME ZONE", "precisionMin": 1, "precisionMax": 6}}, {"sourceType": {"name": "DBCLOB"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "DECFLOAT"}, "targetType": {"name": "NUMBER"}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "NUMBER"}}, {"sourceType": {"name": "DOUBLE"}, "targetType": {"name": "NUMBER"}}, {"sourceType": {"name": "INTEGER"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "LONG VARCHAR"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "LONG VARGRAPHIC"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "SMALLINT"}, "targetType": {"name": "NUMBER(5, 0)"}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 16336}, "targetType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 16336}}, {"sourceType": {"name": "VARGRAPHIC", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "VARGRAPHIC", "lengthMin": 4001, "lengthMax": 16336}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "BOOL"}, "targetType": {"name": "VARCHAR2(10)"}}, {"sourceType": {"name": "BOOLEAN"}, "targetType": {"name": "VARCHAR2(10)"}}, {"sourceType": {"name": "XML"}, "targetType": {"name": "XMLTYPE"}}]