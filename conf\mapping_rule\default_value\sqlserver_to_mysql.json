[{}, null, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "\\s*\\(\\s*\\(\\s*(\\s*\\d+\\s*)\\s*\\)\\s*\\)", "replacement": "$1", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "(?i)newid", "replacement": "UUID", "replaceType": "All"}}, {"columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "(?i)newsequentialid", "replacement": "UUID", "replaceType": "All"}}]