[{"sourceType": {"name": "CHAR", "lengthMin": 0, "lengthMax": 0}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 1}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 8001, "lengthMax": 2147483647}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "TINYINT"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "TINYINT", "autoIncrement": true}, "targetType": {"name": "smallint identity"}}, {"sourceType": {"name": "TINYINT UNSIGNED"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "TINYINT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "int identity"}}, {"sourceType": {"name": "SMALLINT", "autoIncrement": true}, "targetType": {"name": "smallint identity"}}, {"sourceType": {"name": "SMALLINT"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "SMALLINT UNSIGNED"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "SMALLINT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "int identity"}}, {"sourceType": {"name": "INT"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "INT", "autoIncrement": true}, "targetType": {"name": "int identity"}}, {"sourceType": {"name": "INTEGER"}, "targetType": {"name": "integer"}}, {"sourceType": {"name": "INT UNSIGNED"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "INT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "bigint identity"}}, {"sourceType": {"name": "BIGINT"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "BIGINT", "autoIncrement": true}, "targetType": {"name": "bigint identity"}}, {"sourceType": {"name": "BIGINT UNSIGNED", "precisionMin": 20, "precisionMax": 38}, "targetType": {"name": "NUMERIC", "precisionMin": 0, "precisionMax": 38}}, {"sourceType": {"name": "BIGINT UNSIGNED"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "BIGINT UNSIGNED", "autoIncrement": true}, "targetType": {"name": "bigint identity"}}, {"sourceType": {"name": "MEDIUMINT"}, "targetType": {"name": "NUMERIC(9, 0)"}}, {"sourceType": {"name": "NUMERIC", "matchExpressions": "precision < scale"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 30}, "targetType": {"name": "NUMERIC", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 30}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 39, "precisionMax": 65}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "DECIMAL", "matchExpressions": "precision < scale"}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 30}, "targetType": {"name": "NUMERIC", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 30}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 39, "precisionMax": 65}, "targetType": {"name": "FLOAT"}}, {"sourceType": {"name": "double"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "float"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "BIT", "lengthMin": 1, "lengthMax": 1}, "targetType": {"name": "bit"}}, {"sourceType": {"name": "BIT", "lengthMin": 2, "lengthMax": 64}, "targetType": {"name": "binary", "lengthMin": 2, "lengthMax": 64}}, {"sourceType": {"name": "VARBIT", "lengthMin": 1, "lengthMax": 64}, "targetType": {"name": "varbinary", "lengthMin": 1, "lengthMax": 64}}, {"sourceType": {"name": "BYTEA"}, "targetType": {"name": "varbinary(max)"}}, {"sourceType": {"name": "YEAR"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(50)"}}, {"sourceType": {"name": "DATETIME"}, "targetType": {"name": "DATETIME2"}}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "DATETIMEOFFSET"}}, {"sourceType": {"name": "TIME"}, "targetType": {"name": "time"}}, {"sourceType": {"name": "SET"}, "targetType": {"name": "var<PERSON><PERSON>(500)"}}, {"sourceType": {"name": "ENUM"}, "targetType": {"name": "var<PERSON><PERSON>(500)"}}, {"sourceType": {"name": "JSON"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "TINYTEXT"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "TEXT"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "LONG TEXT"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "MEDIUMTEXT"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "BINARY", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "binary", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "VARBINARY", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "varbinary", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "varbinary(max)"}}, {"sourceType": {"name": "TINYBLOB"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "MEDIUMBLOB"}, "targetType": {"name": "varbinary(max)"}}, {"sourceType": {"name": "LONGBLOB"}, "targetType": {"name": "varbinary(max)"}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "date"}}, {"sourceType": {"name": "GEOMETRY"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "geography"}, "targetType": {"name": "geography"}}, {"sourceType": {"name": "double unsigned"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "float unsigned"}, "targetType": {"name": "float"}}]