[{"minSourceDbVersion": "V9", "sourceType": {"name": "varbinary", "precisionMin": 8001}, "targetType": {"name": "varbinary(max)"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "rowversion"}, "targetType": {"name": "timestamp"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "bytea"}, "targetType": {"name": "varbinary(max)"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "int4"}, "targetType": {"name": "int"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "int8"}, "targetType": {"name": "bigint"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "bigint"}, "targetType": {"name": "bigint"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "float8"}, "targetType": {"name": "float"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "float4"}, "targetType": {"name": "real"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "smallint"}, "targetType": {"name": "smallint"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "<PERSON><PERSON><PERSON>", "precisionMin": -4, "precisionMax": -4}, "targetType": {"name": "varchar(max)"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "uuid"}, "targetType": {"name": "uniqueidentifier"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "json"}, "targetType": {"name": "nvar<PERSON><PERSON>(max)"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "timestamptz"}, "targetType": {"name": "datetimeoffset"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "<PERSON><PERSON><PERSON>", "precisionMin": 8001}, "targetType": {"name": "varchar(max)"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "INTERVAL"}, "targetType": {"name": "VARCHAR(50)"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "TINTERVAL"}, "targetType": {"name": "VARCHAR(50)"}}, {"minSourceDbVersion": "V9", "sourceType": {"name": "real"}, "targetType": {"name": "real"}}]