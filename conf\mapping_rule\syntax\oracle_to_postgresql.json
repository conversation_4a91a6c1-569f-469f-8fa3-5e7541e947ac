[{}, null, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["VIEW", "FUNCTION", "PROCEDURE"], "regexp": "\\bNVL\\s*\\(", "replacement": "COALESCE(", "replaceType": "All"}, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["VIEW", "FUNCTION", "PROCEDURE"], "regexp": "\\bDECODE\\s*\\(", "replacement": "CASE", "replaceType": "All"}, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["VIEW", "FUNCTION", "PROCEDURE"], "regexp": "\\bROWNUM\\b", "replacement": "ROW_NUMBER() OVER (ORDER BY 1)", "replaceType": "All"}, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["VIEW"], "regexp": "union\\s+all\\s+select", "replacement": "UNION ALL\\nSELECT", "replaceType": "All"}, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["VIEW", "FUNCTION", "PROCEDURE"], "regexp": "\\bListagg\\s*\\(", "replacement": "STRING_AGG(", "replaceType": "All"}, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["VIEW", "FUNCTION", "PROCEDURE"], "regexp": "\\bLISTAGG\\s*\\(", "replacement": "STRING_AGG(", "replaceType": "All"}, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["VIEW", "FUNCTION", "PROCEDURE"], "regexp": "within\\s+GROUP\\s*\\(\\s*ORDER\\s+BY\\s+([^)]+)\\)", "replacement": "ORDER BY $1", "replaceType": "All"}, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["VIEW"], "regexp": "\\bfrom\\s+([a-zA-Z0-9_]+)\\s+union\\s+all\\s+select", "replacement": "FROM $1\\nUNION ALL\\nSELECT", "replaceType": "All"}, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["VIEW", "FUNCTION", "PROCEDURE"], "regexp": "\\|\\|\\s*'\\\\'\\ \\|\\|", "replacement": "|| E'\\\\' ||", "replaceType": "All"}, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["VIEW", "FUNCTION", "PROCEDURE"], "regexp": "\\bLENGTH\\s*\\(([^)]+)\\)\\s*<>\\s*(\\d+)", "replacement": "LENGTH($1) != $2", "replaceType": "All"}, {"schemaIncludes": ["*"], "schemaExcludes": null, "objectIncludes": ["*"], "objectExcludes": null, "objectTypes": ["VIEW", "FUNCTION", "PROCEDURE"], "regexp": "\\bLENGTH\\s*\\(([^)]+)\\)\\s*>\\s*(\\d+)", "replacement": "LENGTH($1) > $2", "replaceType": "All"}]