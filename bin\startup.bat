@echo off

rem %1(start /min cmd.exe /c %0 :&exit)


rem chcp 65001

rem 程序名称和版本
set "KDTS=kdts-app-console"
set "KDTS_VERSION=2.0.2.34"
set "KDTS_JAR=%KDTS%-%KDTS_VERSION%.jar"

rem 设置窗口标题
title %KDTS% %KDTS_VERSION%

rem 进入bin目录和基目录
cd /d %~dp0
set "BIN_PATH=%cd%"
cd ..
set "BASE_PATH=%cd%"

rem JAVA 路径 (优先使用 JAVA_PATH 目录下的 jdk)
set "JAVA_PATH=%BASE_PATH%/jdk"
set "REQUIRED_JAVA_VERSION=11"

set JAVA_CMD=""
set JAVA_VERSION=""
set "JAVA_VERSION_JAVA_PATH=0"
set JAVA_CMD_JAVA_PATH="%JAVA_PATH%\bin\javaw.exe"
if exist "%JAVA_PATH%\bin\javaw.exe" (
    for /f "tokens=4" %%a in ('"%JAVA_PATH%\bin\javaw.exe" -fullversion 2^>^&1') do (
		set "JAVA_VERSION_JAVA_PATH=%%a"
    )
)
call :compareVersionEx %JAVA_VERSION_JAVA_PATH% %REQUIRED_JAVA_VERSION% result1
if %result1% GEQ 0 (
    set JAVA_CMD=%JAVA_CMD_JAVA_PATH%%
    set JAVA_VERSION=%JAVA_VERSION_JAVA_PATH%
)

if not %JAVA_CMD%=="" goto L_HAS_JAVA_CMD
set JAVA_VERSION_JAVA_HOME="0"
if exist "%JAVA_HOME%\bin\javaw.exe" (
    for /f "tokens=4" %%a in ('"%JAVA_HOME%\bin\javaw.exe" -fullversion 2^>^&1') do (
		set JAVA_CMD_JAVA_HOME="%JAVA_HOME%\bin\javaw.exe"
		set JAVA_VERSION_JAVA_HOME=%%a
    )
)
call :compareVersionEx %JAVA_VERSION_JAVA_HOME% %REQUIRED_JAVA_VERSION% result2
if %result2% GEQ 0 (
    set JAVA_CMD=%JAVA_CMD_JAVA_HOME%%
    set JAVA_VERSION=%JAVA_VERSION_JAVA_HOME%
)

if not %JAVA_CMD%=="" goto L_HAS_JAVA_CMD
set JAVA_VERSION_JRE_HOME="0"
if exist "%JRE_HOME%\bin\javaw.exe" (
    for /f "tokens=4" %%a in ('"%JRE_HOME%\bin\javaw.exe" -fullversion 2^>^&1') do (
		set JAVA_CMD_JRE_HOME="%JRE_HOME%\bin\javaw.exe"
		set JAVA_VERSION_JRE_HOME=%%a
    )
)
call :compareVersionEx %JAVA_VERSION_JRE_HOME% %REQUIRED_JAVA_VERSION% result3
if %result3% GEQ 0 (
    set JAVA_CMD=%JAVA_CMD_JRE_HOME%%
    set JAVA_VERSION=%JAVA_VERSION_JRE_HOME%
)
if not %JAVA_CMD%=="" goto L_HAS_JAVA_CMD
set JAVA_VERSION=%JAVA_VERSION_JAVA_PATH%
:L_HAS_JAVA_CMD

rem 外部配置文件绝对目录,如果是目录需要/结尾，也可以直接指定文件
rem 如果指定的是目录,spring则会读取目录中的所有配置文件
set "CONFIG_DIR=%BASE_PATH%/conf"

rem 格式化时间
:: Check WMIC is available
WMIC.EXE Alias /? >NUL 2>&1 || GOTO L_NO_WMIC
for /f %%# in ('WMIC Path Win32_LocalTime Get /Format:value') do @for /f %%@ in ("%%#") do @set %%@
for /f "tokens=2 delims==" %%a in ('wmic.exe os get TotalVisibleMemorySize /value') do (
  set /a TOTAL_MEMORY=%%a / 1024
)
for /f "tokens=2 delims==" %%a in ('wmic.exe os get FreePhysicalMemory /value') do (
  set /a FREE_MEMORY=%%a / 1024
)
goto L_FORMAT_TIME

:L_NO_WMIC
set /a year=%date:~0,4%
set /a month=%date:~5,2%
set /a day=%date:~8,2%
set /a hour=%time:~0,2%
set /a minute=%time:~3,2%
set /a second=%time:~6,2%
set /a TOTAL_MEMORY=1024
set /a FREE_MEMORY=512

:L_FORMAT_TIME
set NOW_STR=%year%-
if %month% LSS 10 (set NOW_STR=%NOW_STR%0)
set NOW_STR=%NOW_STR%%month%-
if %day% LSS 10 (set NOW_STR=%NOW_STR%0)
set NOW_STR=%NOW_STR%%day%_
if %hour% LSS 10 (set NOW_STR=%NOW_STR%0)
set NOW_STR=%NOW_STR%%hour%-
if %minute% LSS 10 (set NOW_STR=%NOW_STR%0)
set NOW_STR=%NOW_STR%%minute%-
if %second% LSS 10 (set NOW_STR=%NOW_STR%0)
set NOW_STR=%NOW_STR%%second%

rem 项目日志输出绝对路径
set "LOG_DIR=%BASE_PATH%/logs"
set "LOG_FILE=%LOG_DIR%/%KDTS%_%NOW_STR%.log"

rem 创建日志目录
if not exist "%LOG_DIR%" (
	md "%LOG_DIR%"
)

rem 根据可用内存大小（三分之二）计算分配给JAVA的内存及使用哪个线程配置文件
set /a JAVA_MEMORY=%FREE_MEMORY%
if %FREE_MEMORY% geq 36864 (
    set JAVA_MEMORY=24G
) else if %FREE_MEMORY% geq 24576 (
    set JAVA_MEMORY=16G
 ) else if %FREE_MEMORY% geq 12288 (
    set JAVA_MEMORY=8G
) else if %FREE_MEMORY% geq 6144 (
    set JAVA_MEMORY=4G
) else if %FREE_MEMORY% geq 3072 (
    set JAVA_MEMORY=2G
) else (
    set JAVA_MEMORY=%JAVA_MEMORY%M
)
rem 如果需要手动设置，把下面的注释（rem）去掉即可
rem set JAVA_MEMORY=13G

rem =======================================================
rem JVM 参数设置
rem =======================================================
set "JAVA_OPT=-server -Dfile.encoding=UTF-8 -Djava.awt.headless=true -Dcache_enable=true"
set "JAVA_OPT=%JAVA_OPT% -Dconfig.path=^"%CONFIG_DIR%^" -Dlog.path=^"%LOG_DIR%^" -Djavax.xml.parsers.SAXParserFactory=com.sun.org.apache.xerces.internal.jaxp.SAXParserFactoryImpl -Djavax.xml.transform.TransformerFactory=com.sun.org.apache.xalan.internal.xsltc.trax.TransformerFactoryImpl -Djavax.xml.parsers.DocumentBuilderFactory=com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl -Djava.security.properties=./conf/enableLegacyTLS.security -Duser.country=CN -Duser.language=zh -Djsse.enableCBCProtection=false"
set "JAVA_OPT=%JAVA_OPT% --add-opens java.base/jdk.internal.loader=ALL-UNNAMED --add-opens jdk.zipfs/jdk.nio.zipfs=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/java.lang.invoke=ALL-UNNAMED"
set "JAVA_OPT=%JAVA_OPT% -XX:+UseG1GC -XX:G1ReservePercent=20 -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:G1HeapRegionSize=8M -XX:+SegmentedCodeCache  -XX:+PrintCommandLineFlags -XX:+ExplicitGCInvokesConcurrent -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./logs/"
set "JAVA_OPT=%JAVA_OPT% -Xms%JAVA_MEMORY% -Xmx%JAVA_MEMORY%"
rem set "JAVA_OPT=%JAVA_OPT% -Xmn768K -XX:MaxDirectMemorySize=512m -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=512m -Xss1024k"
rem set "JAVA_OPT=%JAVA_OPT% -XX:+UseG1GC -XX:G1ReservePercent=20 -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:G1HeapRegionSize=8M -XX:+SegmentedCodeCache  -verbose:gc -XX:+PrintCommandLineFlags -XX:+ExplicitGCInvokesConcurrent"
rem set "JAVA_OPT=%JAVA_OPT% -XX:+UseG1GC -XX:ConcGCThreads=8 -XX:G1ReservePercent=20 -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:G1HeapRegionSize=8M -XX:+SegmentedCodeCache  -verbose:gc -XX:+PrintCommandLineFlags -XX:+ExplicitGCInvokesConcurrent"
rem set "JAVA_OPT=%JAVA_OPT% -Xlog:gc*,safepoint:%BASE_PATH%/logs/gc.log:time,uptime:filecount=100,filesize=50M"

rem =======================================================
rem 将命令启动相关日志追加到日志文件
rem =======================================================

rem 输出项目名称
echo %KDTS% v%KDTS_VERSION%
echo %KDTS% v%KDTS_VERSION% >> "%LOG_FILE%"
rem 输出jar包名称
echo jar name: %KDTS_JAR%
echo jar name: %KDTS_JAR% >> "%LOG_FILE%"
rem 输出项目根目录
echo base path: %BASE_PATH%
echo base path: %BASE_PATH% >> "%LOG_FILE%"
rem 输出项目bin路径
echo bin path: %BIN_PATH%
echo bin path: %BIN_PATH% >> "%LOG_FILE%"
rem 输出项目config路径
echo config path: %CONFIG_DIR%
echo config path: %CONFIG_DIR% >> "%LOG_FILE%"
rem 打印日志路径
echo log dir: %LOG_DIR%
echo log dir: %LOG_DIR% >> "%LOG_FILE%"
rem 打印日志文件
echo log file: %LOG_FILE%
echo log file: %LOG_FILE% >> "%LOG_FILE%"
rem 打印迁移名称
echo migration name: %NOW_STR%
echo migration name: %NOW_STR% >> "%LOG_FILE%"
rem 打印java路径
echo java: %JAVA_CMD%
echo java: %JAVA_CMD% >> %LOG_FILE%
rem 打印JVM路径
echo JAVA_PATH: %JAVA_PATH%
echo JAVA_PATH: %JAVA_PATH% >> %LOG_FILE%
rem 打印JVM配置
echo JAVA_OPTS: %JAVA_OPT%
echo JAVA_OPTS: ^"%JAVA_OPT%^" >> "%LOG_FILE%"
rem 打印内存配置
echo FreeMemory=%FREE_MEMORY%M JavaMemory=%JAVA_MEMORY%

rem 打印启动命令
echo startup command: %JAVA_CMD% %JAVA_OPT% -jar %BASE_PATH%/%KDTS_JAR% >> "%LOG_FILE%"

rem ======================================================================
rem 执行启动命令：后台启动项目,并将日志输出到项目根目录下的logs文件夹下
rem ======================================================================

if not %JAVA_CMD%=="" goto L_JAVA_CMD_OK
if %JAVA_CMD%=="" (
	echo Unable to find JDK,please set JAVA_PATH or copy jdk to the installation package's JDK
	pause
	goto: eof
)
echo %KDTS% %KDTS_VERSION%
echo "JAVA_PATH=%JAVA_PATH%"
if not %JAVA_VERSION%=="0" (
    echo "Error: The java version ('%JAVA_VERSION%') of '%JAVA_PATH%\bin\javaw.exe' is too old. It must have at least '%REQUIRED_JAVA_VERSION%' installed."
)else (
    echo "Error: The '%JAVA_PATH%\bin\javaw.exe' does not exist, it is needed to run this program. Please check the 'JAVA_PATH' in 'startup.bat'."
)
cd "%BIN_PATH%"
goto :eof

:L_JAVA_CMD_OK

echo ------------------------------------------------------------------------
@call %JAVA_CMD% -version
echo ------------------------------------------------------------------------

set "PID_FILE=%BASE_PATH%\pid"
if exist %PID_FILE% (
	for /f "delims=" %%i in (%PID_FILE%) do (
		echo %KDTS% is already start, please close it.
		pause
		goto:eof
	)

)

start "kdts-app-console" /B %JAVA_CMD% %JAVA_OPT% -jar "%BASE_PATH%/lib/%KDTS_JAR%"

timeout /t 5 /nobreak


rem PID文件
set "PID_FILE=%BASE_PATH%\pid"

if exist %PID_FILE% (
	for /f "delims=" %%i in (%PID_FILE%) do (
		echo "Start success, See "%CONFIG_DIR%/log/* for more detail", you can close it."
		pause
		goto :stop
	)
) else (
	echo See "./%NOW_STR%/*/info.log or warn.log or error.log" for more detail. >> "%LOG_FILE%"
	pause
)

cd %BIN_PATH%

goto :eof

:compareVersionEx
call :compareVersion %1 %2
set "%~3=-1"
if %errorlevel% == 1 set "%~3=1"
if %errorlevel% == 0 set "%~3=0"
exit /b

:compareVersion version1 version2
setlocal enableDelayedExpansion
set "value1=%~1"
set "value2=%~2"
call :extractLetters value1
call :extractLetters value2
:L_LOOP_COMPARE_VERSION


call :parseValue "%value1%" n1 value1
call :parseValue "%value2%" n2 value2
if %n1% GTR %n2% exit /b 1
if %n1% LSS %n2% exit /b -1
if not defined value1 if not defined value2 exit /b 0
if not defined value1 exit /b -1
if not defined value2 exit /b 1
goto :L_LOOP_COMPARE_VERSION

:parseValue version varValue remainderValue
for /f "tokens=1* delims=.,-" %%A in ("%~1") do (
    set "%~2=%%A"
	set "%~3=%%B"
)
exit /b

:extractLetters versionVar
for %%C in (a b c d e f g h i j k l m n o p q r s t u v w x y z) do set "%~1=!%~1:%%C=.%%C!"
exit /b
