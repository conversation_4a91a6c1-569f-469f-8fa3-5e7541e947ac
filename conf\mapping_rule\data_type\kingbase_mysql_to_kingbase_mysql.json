[{"sourceType": {"name": "int unsigned"}, "targetType": {"name": "int unsigned"}}, {"sourceType": {"name": "bigint unsigned"}, "targetType": {"name": "bigint unsigned"}}, {"sourceType": {"name": "datetime"}, "targetType": {"name": "datetime"}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "BINARY", "lengthMin": 0, "lengthMax": 2147483647}, "targetType": {"name": "BINARY", "lengthMin": 0, "lengthMax": 2147483647}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "VARBINARY", "lengthMin": 0, "lengthMax": 2147483647}, "targetType": {"name": "VARBINARY", "lengthMin": 0, "lengthMax": 2147483647}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "float"}, "targetType": {"name": "float"}}]