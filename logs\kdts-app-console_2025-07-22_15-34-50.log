kdts-app-console v2.0.2.34 
jar name: kdts-app-console-2.0.2.34.jar 
base path: D:\kdts-plus-bs-V009R001C002 
bin path: D:\kdts-plus-bs-V009R001C002\bin 
config path: D:\kdts-plus-bs-V009R001C002/conf 
log dir: D:\kdts-plus-bs-V009R001C002/logs 
log file: D:\kdts-plus-bs-V009R001C002/logs/kdts-app-console_2025-07-22_15-34-50.log 
migration name: 2025-07-22_15-34-50 
java: "D:\kdts-plus-bs-V009R001C002/jdk\bin\javaw.exe" 
JAVA_PATH: D:\kdts-plus-bs-V009R001C002/jdk 
JAVA_OPTS: "-server -Dfile.encoding=UTF-8 -Djava.awt.headless=true -Dcache_enable=true -Dconfig.path="D:\kdts-plus-bs-V009R001C002/conf" -Dlog.path="D:\kdts-plus-bs-V009R001C002/logs" -Djavax.xml.parsers.SAXParserFactory=com.sun.org.apache.xerces.internal.jaxp.SAXParserFactoryImpl -Djavax.xml.transform.TransformerFactory=com.sun.org.apache.xalan.internal.xsltc.trax.TransformerFactoryImpl -Djavax.xml.parsers.DocumentBuilderFactory=com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl -Djava.security.properties=./conf/enableLegacyTLS.security -Duser.country=CN -Duser.language=zh -Djsse.enableCBCProtection=false --add-opens java.base/jdk.internal.loader=ALL-UNNAMED --add-opens jdk.zipfs/jdk.nio.zipfs=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/java.lang.invoke=ALL-UNNAMED -XX:+UseG1GC -XX:G1ReservePercent=20 -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:G1HeapRegionSize=8M -XX:+SegmentedCodeCache  -XX:+PrintCommandLineFlags -XX:+ExplicitGCInvokesConcurrent -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./logs/ -Xms4G -Xmx4G^" 
