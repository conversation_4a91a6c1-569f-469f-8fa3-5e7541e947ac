[{"sourceType": {"name": "bytea"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "bpchar", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "TIMESTAMPTZ", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "TIMESTAMP datetime year to fraction", "lengthMin": 1, "lengthMax": 5}}, {"sourceType": {"name": "TIMESTAMP", "lengthMin": 1, "lengthMax": 6}, "targetType": {"name": "TIMESTAMP datetime year to fraction", "lengthMin": 1, "lengthMax": 5}}, {"sourceType": {"name": "float8"}, "targetType": {"name": "DOUBLE PRECISION"}}, {"sourceType": {"name": "XML"}, "targetType": {"name": "XMLType"}}, {"sourceType": {"name": "interval year to month"}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 255, "lengthMax": 8000}}, {"sourceType": {"name": "interval year to day"}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 255, "lengthMax": 8000}}, {"sourceType": {"name": "interval day to second"}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 255, "lengthMax": 8000}}, {"sourceType": {"name": "json"}, "targetType": {"name": "json"}}, {"sourceType": {"name": "bool"}, "targetType": {"name": "boolean"}}, {"sourceType": {"name": "polygon"}, "targetType": {"name": "VARCHAR(197)"}}, {"sourceType": {"name": "TSVECTOR"}, "targetType": {"name": "VARCHAR(2046)"}}, {"sourceType": {"name": "inet"}, "targetType": {"name": "VARCHAR(2046)"}}, {"sourceType": {"name": "jsonb"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "BPCHARBYTE", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "pg_catalog.interval"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(50)"}}, {"sourceType": {"name": "box"}, "targetType": {"name": "VARCHAR(2046)"}}, {"sourceType": {"name": "point"}, "targetType": {"name": "VARCHAR(2046)"}}, {"sourceType": {"name": "_INT4"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "_text"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "LSEG"}, "targetType": {"name": "VARCHAR(100)"}}, {"sourceType": {"name": "VARCHARBYTE", "lengthMin": 1, "lengthMax": 8188}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8188}}, {"sourceType": {"name": "TIME"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(20)"}}, {"sourceType": {"name": "TIMETZ"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(50)"}}, {"sourceType": {"name": "varbit"}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "UUID"}, "targetType": {"name": "VARCHAR(2046)"}}, {"sourceType": {"name": "CIDR"}, "targetType": {"name": "VARCHAR(50)"}}, {"sourceType": {"name": "CIRCLE"}, "targetType": {"name": "VARCHAR(73)"}}, {"sourceType": {"name": "bit"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(100)"}}, {"sourceType": {"name": "MACADDR"}, "targetType": {"name": "VARCHAR(50)"}}, {"sourceType": {"name": "PATH"}, "targetType": {"name": "VARCHAR(140)"}}, {"sourceType": {"name": "TSQUERY"}, "targetType": {"name": "VARCHAR(2046)"}}]