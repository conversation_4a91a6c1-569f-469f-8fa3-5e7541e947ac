[{}, null, {"minSourceDbVersion": "12c", "minTargetDbVersion": "V8R6", "columnTypeIncludes": ["NUMBER"], "type": "RegexpReplace", "content": {"regexp": "(?<schemaName>\\S+)\\.(?<sequenceName>\\S+)\\.\"?(?<keyWord>NEXTVAL)\"?", "replacement": "${keyWord}('${schemaName}.${sequenceName}'::regclass)", "replaceType": "All"}}, {"minSourceDbVersion": "12c", "minTargetDbVersion": "V8R6", "columnTypeIncludes": ["*"], "type": "RegexpReplace", "content": {"regexp": "TO_CHAR\\(\\\"([^\\\"]+\\\"\\.\\\"[^\\\"]+)\\\"\\.\\\"NEXTVAL\\\"\\)", "replacement": "NEXTVAL('\\\"$1\\\"')", "replaceType": "All"}}]