[{"sourceType": {"name": "BFILE"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "DATE"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 4, "autoIncrement": true}, "targetType": {"name": "smallserial"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 5, "precisionMax": 9, "autoIncrement": true}, "targetType": {"name": "serial"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 10, "precisionMax": 18, "autoIncrement": true}, "targetType": {"name": "bigserial"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 0}}, {"sourceType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 0, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "numeric", "precisionMin": 38, "precisionMax": 38, "scaleMin": 0, "scaleMax": 0}}, {"sourceType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 0, "scaleMin": -127, "scaleMax": -127}, "targetType": {"name": "numeric"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 38, "scaleMin": -84, "scaleMax": -1}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": -84, "scaleMax": -1, "attachNegativeScaleSize": false}}, {"sourceType": {"name": "NUMBER", "precisionMin": 0, "precisionMax": 38, "scaleMin": 1, "scaleMax": 127}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 1, "scaleMax": 127}}, {"sourceType": {"name": "NCHAR", "lengthMin": 1, "lengthMax": 1000, "charUsedSupport": true}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 1000}}, {"sourceType": {"name": "NVARCHAR2", "lengthMin": 1, "lengthMax": 2000, "charUsedSupport": true}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 4000, "charUsedSupport": true}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "NCLOB"}, "targetType": {"name": "nclob"}}, {"sourceType": {"name": "SDE.ST_GEOMETRY"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "SDE.SDO_GEOMETRY"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "MDSYS.SDO_GEOMETRY"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "MDSYS.SDO_GEORASTER"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "MDSYS.SDO_POINT_TYPE"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "MDSYS.SDO_TOPO_GEOMETRY"}, "targetType": {"name": "topogeometry"}}, {"sourceType": {"name": "MDSYS.ST_GEOMETRY"}, "targetType": {"name": "geometry"}}, {"maxTargetDbVersion": "V8R6", "sourceType": {"name": "INTERVALYM"}, "targetType": {"name": "intervalym"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL(\\s*)YEAR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)MONTH", "INTERVAL(\\s*)YEAR(\\s*)TO(\\s*)MONTH"], "regularReplacements": ["interval year to month", "interval year to month"]}, {"minTargetDbVersion": "V8R6C7", "sourceType": {"name": "INTERVALYM"}, "targetType": {"name": "intervalym"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL(\\s*)YEAR(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)MONTH", "INTERVAL(\\s*)YEAR(\\s*)TO(\\s*)MONTH"], "regularReplacements": ["interval year(${PRECISION}) to month", "interval year(${PRECISION}) to month"]}, {"maxTargetDbVersion": "V8R6", "sourceType": {"name": "INTERVALDS"}, "targetType": {"name": "INTERVALDS"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND"], "regularReplacements": ["interval day to second(${SCALE})", "interval day to second", "interval day to second(${SCALE})", "interval day to second"]}, {"minTargetDbVersion": "V8R6C7", "sourceType": {"name": "INTERVALDS"}, "targetType": {"name": "INTERVALDS"}, "regexMatchReplace": true, "regularExpressions": ["INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)TO(\\s*)SECOND", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND(\\s*)(\\((?<precision2>\\d*)\\))", "INTERVAL(\\s*)DAY(\\s*)TO(\\s*)SECOND"], "regularReplacements": ["interval day(${PRECISION}) to second(${SCALE})", "interval day(${PRECISION}) to second", "interval day(${PRECISION}) to second(${SCALE})", "interval day(${PRECISION}) to second"]}, {"sourceType": {"name": "LONG"}, "targetType": {"name": "text"}}, {"minTargetDbVersion": "V8R6C5B54", "sourceType": {"name": "LONG RAW"}, "targetType": {"name": "long raw"}}, {"sourceType": {"name": "LONG RAW"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "SYS.XMLTYPE"}, "targetType": {"name": "xml"}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "double precision"}}, {"minTargetDbVersion": "V8R6C5B54", "sourceType": {"name": "RAW", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "raw", "lengthMin": 1, "lengthMax": 2000}}, {"minTargetDbVersion": "V7", "sourceType": {"name": "RAW"}, "targetType": {"name": "bytea"}}, {"minTargetDbVersion": "V7", "sourceType": {"name": "LONG RAW"}, "targetType": {"name": "bytea"}}, {"sourceType": {"name": "MDSYS.MBRCOORDLIST"}, "targetType": {"name": "numeric array"}}, {"sourceType": {"name": "BINARY_DOUBLE"}, "targetType": {"name": "binary_double"}}, {"sourceType": {"name": "BINARY_FLOAT"}, "targetType": {"name": "binary_float"}}, {"sourceType": {"name": "timestamptz", "scaleMin": 0, "scaleMax": 6}, "targetType": {"name": "timestamptz", "scaleMin": 0, "scaleMax": 6}}]