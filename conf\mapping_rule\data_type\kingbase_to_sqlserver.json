[{"sourceType": {"name": "tinyint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "int2"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "xml"}, "targetType": {"name": "xml"}}, {"sourceType": {"name": "character", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "char", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "nchar", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "char", "lengthMin": 4001, "lengthMax": 2147483647}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "nchar", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "nchar", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "nchar", "lengthMin": 4001, "lengthMax": 2147483647}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "int", "autoIncrement": true}, "targetType": {"name": "int identity"}}, {"sourceType": {"name": "integer", "autoIncrement": true}, "targetType": {"name": "int identity"}}, {"sourceType": {"name": "serial", "autoIncrement": true}, "targetType": {"name": "int identity"}}, {"sourceType": {"name": "oid"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "regclass"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "bigint", "autoIncrement": true}, "targetType": {"name": "bigint identity"}}, {"sourceType": {"name": "bigserial", "autoIncrement": true}, "targetType": {"name": "bigint identity"}}, {"sourceType": {"name": "smallint", "autoIncrement": true}, "targetType": {"name": "smallint identity"}}, {"sourceType": {"name": "smallserial", "autoIncrement": true}, "targetType": {"name": "smallint identity"}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 4001, "lengthMax": 2147483647}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 4001, "lengthMax": 2147483647}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON><PERSON>", "lengthMin": 4001, "lengthMax": 2147483647}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "bpchar", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "bpchar", "lengthMin": 4001, "lengthMax": 2147483647}, "targetType": {"name": "text"}}, {"sourceType": {"name": "bp<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "bp<PERSON><PERSON><PERSON>", "lengthMin": 4001, "lengthMax": 2147483647}, "targetType": {"name": "text"}}, {"sourceType": {"name": "nclob"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "clob"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "name"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(64)"}}, {"sourceType": {"name": "cidr"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(20)"}}, {"sourceType": {"name": "text"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "blob"}, "targetType": {"name": "varbinary(max)"}}, {"sourceType": {"name": "int4"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "bool"}, "targetType": {"name": "bit"}}, {"sourceType": {"name": "timetz"}, "targetType": {"name": "time"}}, {"sourceType": {"name": "int8"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "interval"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(50)"}}, {"sourceType": {"name": "pg_catalog.interval"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(50)"}}, {"sourceType": {"name": "real"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "double"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "float"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "float4"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "float8"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "abstime"}, "targetType": {"name": "datetime2"}}, {"sourceType": {"name": "timestamp with timezone"}, "targetType": {"name": "datetimeoffset"}}, {"sourceType": {"name": "timestamptz"}, "targetType": {"name": "datetimeoffset"}}, {"sourceType": {"name": "date"}, "targetType": {"name": "datetime2"}}, {"sourceType": {"name": "timestamp"}, "targetType": {"name": "datetime2"}}, {"sourceType": {"name": "timestamp without timezone"}, "targetType": {"name": "datetime2"}}, {"sourceType": {"name": "bit", "lengthMin": 1, "lengthMax": 1}, "targetType": {"name": "bit"}}, {"sourceType": {"name": "bit", "lengthMin": 2, "lengthMax": 255}, "targetType": {"name": "binary", "lengthMin": 2, "lengthMax": 255}}, {"sourceType": {"name": "varbit", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "varbinary", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "varbit", "lengthMin": 8001, "lengthMax": 2147483647}, "targetType": {"name": "varbinary(8000)"}}, {"sourceType": {"name": "inet"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(50)"}}, {"sourceType": {"name": "money"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(100)"}}, {"sourceType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 0}}, {"sourceType": {"name": "numeric", "precisionMin": 0, "precisionMax": 0, "scaleMin": 0, "scaleMax": 0}, "targetType": {"name": "numeric", "precisionMin": 38, "precisionMax": 38, "scaleMin": 0, "scaleMax": 0}}, {"sourceType": {"name": "numeric", "precisionMin": 0, "precisionMax": 0, "scaleMin": -127, "scaleMax": -127}, "targetType": {"name": "numeric"}}, {"sourceType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": -84, "scaleMax": -1}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": -84, "scaleMax": -1, "attachNegativeScaleSize": false}}, {"sourceType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 1, "scaleMax": 127}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 1, "scaleMax": 127}}, {"sourceType": {"name": "bytea"}, "targetType": {"name": "varbinary(max)"}}, {"sourceType": {"name": "point"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(46)"}}, {"sourceType": {"name": "line"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(30)"}}, {"sourceType": {"name": "polygon"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(197)"}}, {"sourceType": {"name": "box"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(97)"}}, {"sourceType": {"name": "circle"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(73)"}}, {"sourceType": {"name": "lseg"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(100)"}}, {"sourceType": {"name": "path"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(140)"}}, {"sourceType": {"name": "_int4"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "_text"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "raster"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "geography"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "geometry"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "json"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "jsonb"}, "targetType": {"name": "varbinary"}}, {"sourceType": {"name": "<PERSON><PERSON>dr"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(50)"}}, {"sourceType": {"name": "tsquery"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(2046)"}}, {"sourceType": {"name": "tsvector"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(2046)"}}, {"sourceType": {"name": "uuid"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(2046)"}}]