此目录是“源表”到“目标表”的数据映射规则定义，迁移程序将会把源表（可以是多个）中的数据写到目标表中。
1. 文件命名  
   对任意源库和目标库，按它们的类型和版本，可同时配置两个文件，如果两个文件都存在，都会被读取，但有版本号所定义的规则优先于无版本号（源数据库和目标数据的类型和版本见conf/datasource-XXX.yml中的“dbType”和“dbVersion”）。  
   （1）有版本号（文件名小写）  
   源数据库类型-源数据库版本号_to_目标数据库类型-目标数据库版本号.json，例如：oracle-11g_to_kingbase-v8r6.json  
   （2）无版本号（文件名小写）  
   源数据库类型_to_目标数据库类型.json，例如：oracle_to_kingbase.json  
   注意：
   （1）通用的映射配置在“无版本号”的文件中，特定版本个性化的映射配置在“有版本号”的文件中。  
   （2）为了避免大小写敏感影响配置文件的读取，文件名一律小写。

2. 映射定义（参见sample.json）
   [{}, null, {  
      "sourceTables": ["TABLE1", "TABLE2", "TABLE3"],//源表  
      "targetTable": "TABLE0"//目标表  
   }, {  
      "schemaIncludes": ["*"],//包含的模式名  
      "schemaExcludes": null,//排除的模式名  
      "sourceTables": ["TABLE1"],//源表  
      "targetTable": "TABLE0"//目标表  
   }]