[{"sourceType": {"name": "GRAPHIC", "lengthMin": 1, "lengthMax": 128}, "targetType": {"name": "GRAPHIC", "lengthMin": 1, "lengthMax": 128}}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "DATE"}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "DECFLOAT"}}, {"sourceType": {"name": "REAL"}, "targetType": {"name": "REAL"}}, {"sourceType": {"name": "VARCHAR"}, "targetType": {"name": "VARCHAR"}}, {"sourceType": {"name": "NVARCHAR"}, "targetType": {"name": "VARCHAR"}}, {"sourceType": {"name": "LONG"}, "targetType": {"name": "DBCLOB"}}, {"sourceType": {"name": "XMLTYPE"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "BFILE"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255, "charUsedSupport": false}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "CHAR", "lengthMin": 256, "lengthMax": 9999, "charUsedSupport": false}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "NCHAR", "lengthMin": 1, "lengthMax": 255, "charUsedSupport": false}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "NCHAR", "lengthMin": 256, "lengthMax": 9999, "charUsedSupport": false}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "DBCLOB"}}, {"sourceType": {"name": "NCLOB"}, "targetType": {"name": "DBCLOB"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "LONG RAW"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "TIMESTAMP WITH TIMEZONE"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "TIMESTAMP WITH TIME ZONE"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "TIMESTAMP WITHOUT TIMEZONE"}, "targetType": {"name": "DATETIME"}}, {"sourceType": {"name": "TIMESTAMP WITH LOCAL TIMEZONE"}, "targetType": {"name": "DATETIME"}}, {"sourceType": {"name": "RAW"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "INTERVAL YEAR"}, "targetType": {"name": "CHAR(12)"}}, {"sourceType": {"name": "INTERVAL DAY"}, "targetType": {"name": "CHAR(25)"}}, {"sourceType": {"name": "BINARY_FLOAT"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "BINARY_DOUBLE"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "ROWID"}, "targetType": {"name": "CHAR(18)"}}, {"sourceType": {"name": "UROWID", "lengthMin": 1, "lengthMax": 99999}, "targetType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 99999}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 31, "scaleMin": 0, "scaleMax": 31}, "targetType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 31, "scaleMin": 0, "scaleMax": 31}}]