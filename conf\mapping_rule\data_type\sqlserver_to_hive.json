[{"sourceType": {"name": "INT"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "INT unsigned"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "tinyint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "tinyint unsigned"}, "targetType": {"name": "tinyint"}}, {"sourceType": {"name": "mediumint"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "mediumint unsigned"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "year"}, "targetType": {"name": "int"}}, {"sourceType": {"name": "bigint"}, "targetType": {"name": "bigint"}}, {"sourceType": {"name": "bigint unsigned"}, "targetType": {"name": "DECIMAL", "precisionMin": 0, "precisionMax": 38}}, {"sourceType": {"name": "float"}, "targetType": {"name": "double"}}, {"sourceType": {"name": "float unsigned"}, "targetType": {"name": "double"}}, {"sourceType": {"name": "double"}, "targetType": {"name": "double"}}, {"sourceType": {"name": "double unsigned"}, "targetType": {"name": "double"}}, {"sourceType": {"name": "smallint"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "smallint unsigned"}, "targetType": {"name": "smallint"}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 65355}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 65355}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 65356, "lengthMax": 10485760}, "targetType": {"name": "string"}}, {"sourceType": {"name": "NVARCHAR", "lengthMin": 1, "lengthMax": 32677}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthExpressions": "length * 2"}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 32678, "lengthMax": 10485760}, "targetType": {"name": "string"}}, {"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}, "targetType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 255}}, {"sourceType": {"name": "CHAR", "lengthMin": 256, "lengthMax": 8000}, "targetType": {"name": "string"}}, {"sourceType": {"name": "NCHAR", "lengthMin": 1, "lengthMax": 127}, "targetType": {"name": "CHAR", "lengthExpressions": "length * 2"}}, {"sourceType": {"name": "NCHAR", "lengthMin": 128, "lengthMax": 8000}, "targetType": {"name": "string"}}, {"sourceType": {"name": "text"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "ntext"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "tinytext"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(255)"}}, {"sourceType": {"name": "MEDIUMTEXT"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "time"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "json"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "xml"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "longtext"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "binary"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "varbinary"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "tinyblob"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "MEDIUMBLOB"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "blob"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "bit"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "date"}, "targetType": {"name": "date"}}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "image"}, "targetType": {"name": "binary"}}, {"sourceType": {"name": "datetime"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "datetime2"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "datetimeoffset"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "smallmoney"}, "targetType": {"name": "DECIMAL(10, 4)"}}, {"sourceType": {"name": "money"}, "targetType": {"name": "DECIMAL(19, 4)"}}, {"sourceType": {"name": "real"}, "targetType": {"name": "double"}}, {"sourceType": {"name": "smalldatetime"}, "targetType": {"name": "timestamp"}}, {"sourceType": {"name": "uniqueidentifier"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(40)"}}, {"sourceType": {"name": "sysname"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(128)"}}, {"sourceType": {"name": "GEOMETRY"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "geography"}, "targetType": {"name": "string"}}, {"sourceType": {"name": "sql_variant"}, "targetType": {"name": "string"}}]