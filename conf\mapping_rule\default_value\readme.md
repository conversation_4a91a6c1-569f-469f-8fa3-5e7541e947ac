此目录是“源数据库”到“目标数据库”的字段缺省值映射规则定义。  
1. 文件命名  
对任意源库和目标库，按它们的类型和版本，可同时配置两个文件，如果两个文件都存在，都会被读取，但有版本号所定义的规则优先于无版本号（源数据库和目标数据的类型和版本见conf/datasource-XXX.yml中的“dbType”和“dbVersion”）。  
（1）有版本号（文件名小写）  
源数据库类型-源数据库版本号_to_目标数据库类型-目标数据库版本号.json，例如：oracle-11g_to_kingbase-v8r6.json  
（2）无版本号（文件名小写）  
源数据库类型_to_目标数据库类型.json，例如：oracle_to_kingbase.json  
注意：
（1）通用的映射配置在“无版本号”的文件中，特定版本个性化的映射配置在“有版本号”的文件中。  
（2）为了避免大小写敏感影响配置文件的读取，文件名一律小写。  

2. 映射定义（参见sample.json）  
[{}, null, {  
   "minSourceDbVersion": "12c",//适用最小源数据库版本  
   "maxSourceDbVersion": "19c",//适用最大源数据库版本  
   "minTargetDbVersion": "V8R6",//适用最小目标数据库版本  
   "maxTargetDbVersion": "V10R10",//适用最大目标数据库版本  
   "schemaIncludes": ["*"],//包含的模式名  
   "schemaExcludes": null,//排除的模式名  
   "tableIncludes": [],//包含的表名  
   "tableExcludes": null,//排除的表名  
   "columnIncludes": null,//包含的字段名  
   "columnExcludes": null,//排除的表名  
   "columnTypeIncludes": ["NUMBER"],//包含的字段类型  
   "type": "RegexpReplace",//正则表达式替换  
   "content": {  //将形如 "SPARROW"."FOO_SEQ"."NEXTVAL" 的缺省值转换为 NEXTVAL('SPARROW.FOO_SEQ'::regclass)
        "regexp": "\"?(?<schemaName>\\S+)\"?\\.\"?(?<sequenceName>\\S+)\"?\\.\"?(?<keyWord>NEXTVAL)\"?",  
        "replacement": "${keyWord}('${schemaName}.${sequenceName}'::regclass)",  
        "replaceType": "All"  
   }  
}]
type可以为RegexpReplace（正则表达式替换）、GroovyScript（Groovy脚本）、JavaScript（JavaScript脚本）、LuaScript（Lua脚本）