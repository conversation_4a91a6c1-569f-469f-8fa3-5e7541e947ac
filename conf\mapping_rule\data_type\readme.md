此目录是“源数据库”到“目标数据库”的数据类型映射定义。  
1. 文件命名  
对任意源库和目标库，按它们的类型和版本，可同时配置两个文件，如果两个文件都存在，都会被读取，但有版本号所定义的规则优先于无版本号（源数据库和目标数据的类型和版本见conf/datasource-XXX.yml的“dbType”和“dbVersion”）。  
（1）有版本号（文件名小写）  
源数据库类型-源数据库版本号_to_目标数据库类型-目标数据库版本号.json，例如：oracle-11g_to_kingbase-v8r6.json  
（2）无版本号（文件名小写）  
源数据库类型_to_目标数据库类型.json，例如：oracle_to_kingbase.json  
注意：
（1）通用的映射配置在“无版本号”的文件中，特定版本个性化的映射配置在“有版本号”的文件中。
（2）为了避免大小写敏感影响配置文件的读取，文件名一律小写。

2. 映射定义（参见datatype-mapping_sample.json）  
[{  
   "minSourceDbVersion": "5.1",//适用最小源数据库版本
   "maxSourceDbVersion": "5.5",//适用最大源数据库版本
   "minTargetDbVersion": "V8R6",//适用最小目标数据库版本
   "maxTargetDbVersion": "V10R10",//适用最大目标数据库版本
    "schemaIncludes": ["*"],//包含的模式名  
    "schemaExcludes": null,//排除的模式名  
    "tableIncludes": [],//包含的表名  
    "tableExcludes": null,//排除的表名  
    "columnIncludes": null,//包含的字段名  
    "columnExcludes": null,//排除的表名  
    "useRegexReplace": false,//是否使用正则表达式匹配替换  
    "regularExpressions": [
        "INTERVAL(\s*)YEAR(\s*)(\((?<precision1>\d*)\))(\s*)TO(\s*)MONTH",
        "INTERVAL(\s*)YEAR(\s*)TO(\s*)MONTH"
    ],//正则表达式集  
    "regularReplacements":[
        "INTERVAL YEAR(${precision1}) TO MONTH",
        "INTERVAL YEAR TO MONTH"
    ],//正则替换式集       
    "sourceType": {//源类型  
        "name": "varchar",//类型名称  
        "lengthMin": null,//长度最小值  
        "lengthMax": "255",//长度最大值  
        "precisionMin": null,//精度（总位数）最小值  
        "precisionMax": null,//精度（总位数）最大值  
        "scaleMin": null,//刻度（小数位数）最小值  
        "scaleMax": null,//刻度（小数位数）最大值  
        "autoIncrement": true,//是否自增  
        "charUsedSupport": true//是否支持使用字符作长度单位（缺省为字节），如：varchar2(8 char) vs varchar2(8 byte)  
   },  
    "targetType": {//目标类型  
        "name": "varchar",//类型名称  
        "lengthMin": 10,//长度最小值  
        "lengthMax": 200,//长度最大值  
        "precisionMin": null,//精度（总位数）最小值  
        "precisionMax": null,//精度（总位数）最大值  
        "scaleMin": null,//刻度（小数位数）最小值  
        "scaleMax": null,//刻度（小数位数）最大值
        "attachNegativeScaleSize": true//是否附加负刻度的长度，即形如NUMBER (4,-2)->numeric(6,0)
    }  
} ]  
其中除了sourceType及其name和targetType及其name为必须项，其余都可不配置或值为null表示不限制。  