2025-07-22 11:23:06.545 INFO  [main] <GLOBAL_THREAD_POOL> c.k.k.t.ThreadPoolManager : Initializing thread pool with default...
  read: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=2500000, daemon=false
  readZ2A: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=2500000, daemon=false
  write: corePoolSize=8, maxPoolSize=8, keepAliveTime=300, workQueueCapacity=12, daemon=false
  writeZ2A: corePoolSize=8, maxPoolSize=8, keepAliveTime=300, workQueueCapacity=12, daemon=false
  metaRead: corePoolSize=1, maxPoolSize=1, keepAliveTime=300, workQueueCapacity=5000000, daemon=false
  metaReadZ2A: corePoolSize=1, maxPoolSize=1, keepAliveTime=300, workQueueCapacity=5000000, daemon=false
  metaWrite: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=10, daemon=false
  metaWriteZ2A: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=10, daemon=false
  writeLargeObject: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=2, daemon=false
2025-07-22 15:30:33.132 INFO  [main] <GLOBAL_THREAD_POOL> c.k.k.t.ThreadPoolManager : Initializing thread pool with default...
  read: corePoolSize=6, maxPoolSize=6, keepAliveTime=300, workQueueCapacity=2500000, daemon=false
  readZ2A: corePoolSize=6, maxPoolSize=6, keepAliveTime=300, workQueueCapacity=2500000, daemon=false
  write: corePoolSize=24, maxPoolSize=24, keepAliveTime=300, workQueueCapacity=36, daemon=false
  writeZ2A: corePoolSize=24, maxPoolSize=24, keepAliveTime=300, workQueueCapacity=36, daemon=false
  metaRead: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=5000000, daemon=false
  metaReadZ2A: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=5000000, daemon=false
  metaWrite: corePoolSize=6, maxPoolSize=6, keepAliveTime=300, workQueueCapacity=10, daemon=false
  metaWriteZ2A: corePoolSize=6, maxPoolSize=6, keepAliveTime=300, workQueueCapacity=10, daemon=false
  writeLargeObject: corePoolSize=12, maxPoolSize=12, keepAliveTime=300, workQueueCapacity=12, daemon=false
2025-07-22 15:33:12.339 INFO  [main] <GLOBAL_THREAD_POOL> c.k.k.t.ThreadPoolManager : Initializing thread pool with default...
  read: corePoolSize=4, maxPoolSize=4, keepAliveTime=300, workQueueCapacity=2500000, daemon=false
  readZ2A: corePoolSize=4, maxPoolSize=4, keepAliveTime=300, workQueueCapacity=2500000, daemon=false
  write: corePoolSize=16, maxPoolSize=16, keepAliveTime=300, workQueueCapacity=24, daemon=false
  writeZ2A: corePoolSize=16, maxPoolSize=16, keepAliveTime=300, workQueueCapacity=24, daemon=false
  metaRead: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=5000000, daemon=false
  metaReadZ2A: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=5000000, daemon=false
  metaWrite: corePoolSize=4, maxPoolSize=4, keepAliveTime=300, workQueueCapacity=10, daemon=false
  metaWriteZ2A: corePoolSize=4, maxPoolSize=4, keepAliveTime=300, workQueueCapacity=10, daemon=false
  writeLargeObject: corePoolSize=8, maxPoolSize=8, keepAliveTime=300, workQueueCapacity=8, daemon=false
2025-07-22 15:34:57.034 INFO  [main] <GLOBAL_THREAD_POOL> c.k.k.t.ThreadPoolManager : Initializing thread pool with default...
  read: corePoolSize=6, maxPoolSize=6, keepAliveTime=300, workQueueCapacity=2500000, daemon=false
  readZ2A: corePoolSize=6, maxPoolSize=6, keepAliveTime=300, workQueueCapacity=2500000, daemon=false
  write: corePoolSize=24, maxPoolSize=24, keepAliveTime=300, workQueueCapacity=36, daemon=false
  writeZ2A: corePoolSize=24, maxPoolSize=24, keepAliveTime=300, workQueueCapacity=36, daemon=false
  metaRead: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=5000000, daemon=false
  metaReadZ2A: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=5000000, daemon=false
  metaWrite: corePoolSize=6, maxPoolSize=6, keepAliveTime=300, workQueueCapacity=10, daemon=false
  metaWriteZ2A: corePoolSize=6, maxPoolSize=6, keepAliveTime=300, workQueueCapacity=10, daemon=false
  writeLargeObject: corePoolSize=12, maxPoolSize=12, keepAliveTime=300, workQueueCapacity=12, daemon=false
2025-07-22 15:36:27.654 INFO  [main] <GLOBAL_THREAD_POOL> c.k.k.t.ThreadPoolManager : Initializing thread pool with default...
  read: corePoolSize=6, maxPoolSize=6, keepAliveTime=300, workQueueCapacity=2500000, daemon=false
  readZ2A: corePoolSize=6, maxPoolSize=6, keepAliveTime=300, workQueueCapacity=2500000, daemon=false
  write: corePoolSize=24, maxPoolSize=24, keepAliveTime=300, workQueueCapacity=36, daemon=false
  writeZ2A: corePoolSize=24, maxPoolSize=24, keepAliveTime=300, workQueueCapacity=36, daemon=false
  metaRead: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=5000000, daemon=false
  metaReadZ2A: corePoolSize=2, maxPoolSize=2, keepAliveTime=300, workQueueCapacity=5000000, daemon=false
  metaWrite: corePoolSize=6, maxPoolSize=6, keepAliveTime=300, workQueueCapacity=10, daemon=false
  metaWriteZ2A: corePoolSize=6, maxPoolSize=6, keepAliveTime=300, workQueueCapacity=10, daemon=false
  writeLargeObject: corePoolSize=12, maxPoolSize=12, keepAliveTime=300, workQueueCapacity=12, daemon=false
