2025-07-22 11:23:03.304 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tom<PERSON> initialized with port(s): 54524 (https) 54523 (http)
2025-07-22 11:23:03.314 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["https-jsse-nio-54524"]
2025-07-22 11:23:03.318 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-54523"]
2025-07-22 11:23:03.324 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-22 11:23:03.325 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.93]
2025-07-22 11:23:03.420 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-22 11:23:03.422 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2280 ms
2025-07-22 11:23:03.645 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-22 11:23:04.059 [main] INFO  o.s.b.autoconfigure.h2.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:file:D:\kdts-plus-bs-V009R001C002/conf/h2/dts'
2025-07-22 11:23:04.275 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - Flyway Community Edition 9.16.1 by Redgate
2025-07-22 11:23:04.276 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-22 11:23:04.278 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - 
2025-07-22 11:23:04.304 [main] INFO  o.f.core.internal.database.base.BaseDatabaseType - Database: jdbc:h2:file:D:\kdts-plus-bs-V009R001C002/conf/h2/dts (H2 2.2)
2025-07-22 11:23:04.337 [main] WARN  org.flywaydb.core.internal.database.base.Database - Flyway upgrade recommended: H2 2.2.220 is newer than this version of Flyway and support has not been tested. The latest supported version of H2 is 2.1.214.
2025-07-22 11:23:04.419 [main] INFO  o.f.c.i.schemahistory.JdbcTableSchemaHistory - Schema history table "PUBLIC"."DTS_SCHEMA_VERSION" does not exist yet
2025-07-22 11:23:04.424 [main] INFO  org.flywaydb.core.internal.command.DbValidate - Successfully validated 46 migrations (execution time 00:00.069s)
2025-07-22 11:23:04.433 [main] INFO  o.f.c.i.schemahistory.JdbcTableSchemaHistory - Creating Schema History table "PUBLIC"."DTS_SCHEMA_VERSION" ...
2025-07-22 11:23:04.476 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Current version of schema "PUBLIC": << Empty Schema >>
2025-07-22 11:23:04.513 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.0.2.64 - init db"
2025-07-22 11:23:04.676 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.2.65 - enable parallel hint"
2025-07-22 11:23:04.705 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.2.67 - table priority list"
2025-07-22 11:23:04.741 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.68 - data blind"
2025-07-22 11:23:04.779 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.78 - owner mapping"
2025-07-22 11:23:04.783 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.80 - tablespace mapping"
2025-07-22 11:23:04.797 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.81 - use insert"
2025-07-22 11:23:04.819 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.91 - remove whitespace"
2025-07-22 11:23:04.832 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.105 - add count size"
2025-07-22 11:23:04.848 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.110 - exclude reserve words"
2025-07-22 11:23:04.865 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.114 - use mysql bit"
2025-07-22 11:23:04.879 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.133 - use mysql collate"
2025-07-22 11:23:04.893 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.137 - write timeout"
2025-07-22 11:23:04.910 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.156 - tablespace mapping object types"
2025-07-22 11:23:04.923 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.161 - lob  threshold size"
2025-07-22 11:23:04.945 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.188 - use table data filter"
2025-07-22 11:23:05.013 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.193 - add kafka"
2025-07-22 11:23:05.082 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.200 - add sqlserver virtual db"
2025-07-22 11:23:05.101 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.201 - update sqlserver virtual db"
2025-07-22 11:23:05.105 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.204 - add table data mapping str db"
2025-07-22 11:23:05.114 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.212 - add column mapping str db"
2025-07-22 11:23:05.127 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.213 - update kdts dbsource database length"
2025-07-22 11:23:05.144 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.229 - update kdts user password length"
2025-07-22 11:23:05.154 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.230 - update kdts user password value"
2025-07-22 11:23:05.168 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.231 - add use dbms stats db"
2025-07-22 11:23:05.180 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.258 - add user role db"
2025-07-22 11:23:05.195 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.263 - modify init user"
2025-07-22 11:23:05.224 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.269 - add task column"
2025-07-22 11:23:05.257 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.270 - add kdts task schema config column"
2025-07-22 11:23:05.262 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.275 - add default auto commit db"
2025-07-22 11:23:05.269 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.285 - add break point db"
2025-07-22 11:23:05.280 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.286 - add start key"
2025-07-22 11:23:05.291 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.287 - add kafka offset"
2025-07-22 11:23:05.303 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.288 - add custom identifier"
2025-07-22 11:23:05.312 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.289 - modify data type filter"
2025-07-22 11:23:05.320 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.290 - add hdfs conf"
2025-07-22 11:23:05.334 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.291 - modify dbsource"
2025-07-22 11:23:05.349 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.292 - add task schema config"
2025-07-22 11:23:05.356 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.293 - add task column cluster name"
2025-07-22 11:23:05.365 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.294 - update kdts user password value"
2025-07-22 11:23:05.374 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "1.6.3.295 - add task hive"
2025-07-22 11:23:05.387 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "2.0.2.1 - add source custom identifier"
2025-07-22 11:23:05.407 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "2.0.2.2 - update kdts user password value"
2025-07-22 11:23:05.413 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "2.0.2.3 - add dws distribute"
2025-07-22 11:23:05.424 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "2.0.2.4 - add validate connection"
2025-07-22 11:23:05.435 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Migrating schema "PUBLIC" to version "******* - add task sequence value"
2025-07-22 11:23:05.444 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Successfully applied 46 migrations to schema "PUBLIC", now at version v******* (execution time 00:00.973s)
2025-07-22 11:23:06.224 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["https-jsse-nio-54524"]
2025-07-22 11:23:06.468 [main] INFO  org.apache.tomcat.util.net.NioEndpoint.certificate - Connector [https-jsse-nio-54524], TLS virtual host [_default_], certificate type [UNDEFINED] configured from keystore [jar:file:/D:/kdts-plus-bs-V009R001C002/lib/kdts-app-console-********.jar!/BOOT-INF/classes!/keystore/kingbase.keystore] using alias [system] with trust store [null]
2025-07-22 11:23:06.492 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-54523"]
2025-07-22 11:23:06.500 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 54524 (https) 54523 (http) with context path ''
2025-07-22 11:23:56.021 [http-nio-54523-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 11:23:56.021 [http-nio-54523-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-22 11:23:56.024 [http-nio-54523-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-22 15:30:30.413 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54524 (https) 54523 (http)
2025-07-22 15:30:30.433 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["https-jsse-nio-54524"]
2025-07-22 15:30:30.438 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-54523"]
2025-07-22 15:30:30.448 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-22 15:30:30.449 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.93]
2025-07-22 15:30:30.650 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-22 15:30:30.651 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2733 ms
2025-07-22 15:30:30.996 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-22 15:30:31.444 [main] INFO  o.s.b.autoconfigure.h2.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:file:D:\kdts-plus-bs-V009R001C002/conf/h2/dts'
2025-07-22 15:30:31.681 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - Flyway Community Edition 9.16.1 by Redgate
2025-07-22 15:30:31.681 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-22 15:30:31.681 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - 
2025-07-22 15:30:31.706 [main] INFO  o.f.core.internal.database.base.BaseDatabaseType - Database: jdbc:h2:file:D:\kdts-plus-bs-V009R001C002/conf/h2/dts (H2 2.2)
2025-07-22 15:30:31.734 [main] WARN  org.flywaydb.core.internal.database.base.Database - Flyway upgrade recommended: H2 2.2.220 is newer than this version of Flyway and support has not been tested. The latest supported version of H2 is 2.1.214.
2025-07-22 15:30:31.845 [main] INFO  org.flywaydb.core.internal.command.DbValidate - Successfully validated 46 migrations (execution time 00:00.085s)
2025-07-22 15:30:31.869 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Current version of schema "PUBLIC": *******
2025-07-22 15:30:31.873 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Schema "PUBLIC" is up to date. No migration necessary.
2025-07-22 15:30:32.667 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["https-jsse-nio-54524"]
2025-07-22 15:30:33.071 [main] INFO  org.apache.tomcat.util.net.NioEndpoint.certificate - Connector [https-jsse-nio-54524], TLS virtual host [_default_], certificate type [UNDEFINED] configured from keystore [jar:file:/D:/kdts-plus-bs-V009R001C002/lib/kdts-app-console-********.jar!/BOOT-INF/classes!/keystore/kingbase.keystore] using alias [system] with trust store [null]
2025-07-22 15:30:33.091 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-54523"]
2025-07-22 15:30:33.094 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 54524 (https) 54523 (http) with context path ''
2025-07-22 15:31:00.204 [http-nio-54523-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 15:31:00.204 [http-nio-54523-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-22 15:31:00.208 [http-nio-54523-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-22 15:33:10.086 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54524 (https) 54523 (http)
2025-07-22 15:33:10.097 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["https-jsse-nio-54524"]
2025-07-22 15:33:10.101 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-54523"]
2025-07-22 15:33:10.107 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-22 15:33:10.108 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.93]
2025-07-22 15:33:10.194 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-22 15:33:10.195 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1994 ms
2025-07-22 15:33:10.432 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-22 15:33:10.853 [main] INFO  o.s.b.autoconfigure.h2.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:file:D:\kdts-plus-bs-V009R001C002/conf/h2/dts'
2025-07-22 15:33:11.059 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - Flyway Community Edition 9.16.1 by Redgate
2025-07-22 15:33:11.060 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-22 15:33:11.060 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - 
2025-07-22 15:33:11.091 [main] INFO  o.f.core.internal.database.base.BaseDatabaseType - Database: jdbc:h2:file:D:\kdts-plus-bs-V009R001C002/conf/h2/dts (H2 2.2)
2025-07-22 15:33:11.121 [main] WARN  org.flywaydb.core.internal.database.base.Database - Flyway upgrade recommended: H2 2.2.220 is newer than this version of Flyway and support has not been tested. The latest supported version of H2 is 2.1.214.
2025-07-22 15:33:11.212 [main] INFO  org.flywaydb.core.internal.command.DbValidate - Successfully validated 46 migrations (execution time 00:00.074s)
2025-07-22 15:33:11.231 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Current version of schema "PUBLIC": *******
2025-07-22 15:33:11.234 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Schema "PUBLIC" is up to date. No migration necessary.
2025-07-22 15:33:12.148 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["https-jsse-nio-54524"]
2025-07-22 15:33:12.293 [main] INFO  org.apache.tomcat.util.net.NioEndpoint.certificate - Connector [https-jsse-nio-54524], TLS virtual host [_default_], certificate type [UNDEFINED] configured from keystore [jar:file:/D:/kdts-plus-bs-V009R001C002/lib/kdts-app-console-********.jar!/BOOT-INF/classes!/keystore/kingbase.keystore] using alias [system] with trust store [null]
2025-07-22 15:33:12.316 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-54523"]
2025-07-22 15:33:12.320 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 54524 (https) 54523 (http) with context path ''
2025-07-22 15:33:57.899 [http-nio-54523-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 15:33:57.899 [http-nio-54523-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-22 15:33:57.901 [http-nio-54523-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-22 15:33:58.588 [http-nio-54523-exec-3] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [181] milliseconds.
2025-07-22 15:33:58.588 [http-nio-54523-exec-2] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [124] milliseconds.
2025-07-22 15:33:58.588 [http-nio-54523-exec-1] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [190] milliseconds.
2025-07-22 15:34:54.747 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54524 (https) 54523 (http)
2025-07-22 15:34:54.757 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["https-jsse-nio-54524"]
2025-07-22 15:34:54.761 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-54523"]
2025-07-22 15:34:54.768 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-22 15:34:54.768 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.93]
2025-07-22 15:34:54.856 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-22 15:34:54.856 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1812 ms
2025-07-22 15:34:55.057 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-22 15:34:55.427 [main] INFO  o.s.b.autoconfigure.h2.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:file:D:\kdts-plus-bs-V009R001C002/conf/h2/dts'
2025-07-22 15:34:55.610 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - Flyway Community Edition 9.16.1 by Redgate
2025-07-22 15:34:55.611 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-22 15:34:55.611 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - 
2025-07-22 15:34:55.634 [main] INFO  o.f.core.internal.database.base.BaseDatabaseType - Database: jdbc:h2:file:D:\kdts-plus-bs-V009R001C002/conf/h2/dts (H2 2.2)
2025-07-22 15:34:55.677 [main] WARN  org.flywaydb.core.internal.database.base.Database - Flyway upgrade recommended: H2 2.2.220 is newer than this version of Flyway and support has not been tested. The latest supported version of H2 is 2.1.214.
2025-07-22 15:34:55.775 [main] INFO  org.flywaydb.core.internal.command.DbValidate - Successfully validated 46 migrations (execution time 00:00.081s)
2025-07-22 15:34:55.802 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Current version of schema "PUBLIC": *******
2025-07-22 15:34:55.806 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Schema "PUBLIC" is up to date. No migration necessary.
2025-07-22 15:34:56.868 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["https-jsse-nio-54524"]
2025-07-22 15:34:57.003 [main] INFO  org.apache.tomcat.util.net.NioEndpoint.certificate - Connector [https-jsse-nio-54524], TLS virtual host [_default_], certificate type [UNDEFINED] configured from keystore [jar:file:/D:/kdts-plus-bs-V009R001C002/lib/kdts-app-console-********.jar!/BOOT-INF/classes!/keystore/kingbase.keystore] using alias [system] with trust store [null]
2025-07-22 15:34:57.016 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-54523"]
2025-07-22 15:34:57.020 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 54524 (https) 54523 (http) with context path ''
2025-07-22 15:35:05.312 [http-nio-54523-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 15:35:05.313 [http-nio-54523-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-22 15:35:05.314 [http-nio-54523-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-22 15:35:06.139 [http-nio-54523-exec-2] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [387] milliseconds.
2025-07-22 15:35:06.140 [http-nio-54523-exec-1] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [429] milliseconds.
2025-07-22 15:35:06.140 [http-nio-54523-exec-4] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [372] milliseconds.
2025-07-22 15:36:25.635 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54524 (https) 54523 (http)
2025-07-22 15:36:25.645 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["https-jsse-nio-54524"]
2025-07-22 15:36:25.649 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-54523"]
2025-07-22 15:36:25.655 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-22 15:36:25.655 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.93]
2025-07-22 15:36:25.742 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-22 15:36:25.743 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1968 ms
2025-07-22 15:36:25.944 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-22 15:36:26.334 [main] INFO  o.s.b.autoconfigure.h2.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:file:D:\kdts-plus-bs-V009R001C002/conf/h2/dts'
2025-07-22 15:36:26.541 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - Flyway Community Edition 9.16.1 by Redgate
2025-07-22 15:36:26.541 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-22 15:36:26.542 [main] INFO  org.flywaydb.core.internal.license.VersionPrinter - 
2025-07-22 15:36:26.564 [main] INFO  o.f.core.internal.database.base.BaseDatabaseType - Database: jdbc:h2:file:D:\kdts-plus-bs-V009R001C002/conf/h2/dts (H2 2.2)
2025-07-22 15:36:26.593 [main] WARN  org.flywaydb.core.internal.database.base.Database - Flyway upgrade recommended: H2 2.2.220 is newer than this version of Flyway and support has not been tested. The latest supported version of H2 is 2.1.214.
2025-07-22 15:36:26.690 [main] INFO  org.flywaydb.core.internal.command.DbValidate - Successfully validated 46 migrations (execution time 00:00.077s)
2025-07-22 15:36:26.707 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Current version of schema "PUBLIC": *******
2025-07-22 15:36:26.710 [main] INFO  org.flywaydb.core.internal.command.DbMigrate - Schema "PUBLIC" is up to date. No migration necessary.
2025-07-22 15:36:27.478 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["https-jsse-nio-54524"]
2025-07-22 15:36:27.613 [main] INFO  org.apache.tomcat.util.net.NioEndpoint.certificate - Connector [https-jsse-nio-54524], TLS virtual host [_default_], certificate type [UNDEFINED] configured from keystore [jar:file:/D:/kdts-plus-bs-V009R001C002/lib/kdts-app-console-********.jar!/BOOT-INF/classes!/keystore/kingbase.keystore] using alias [system] with trust store [null]
2025-07-22 15:36:27.640 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-54523"]
2025-07-22 15:36:27.644 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 54524 (https) 54523 (http) with context path ''
2025-07-22 15:36:52.850 [http-nio-54523-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 15:36:52.850 [http-nio-54523-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-22 15:36:52.855 [http-nio-54523-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
