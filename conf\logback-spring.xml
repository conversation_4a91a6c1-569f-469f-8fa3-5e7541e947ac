<?xml version="1.0" encoding="UTF-8"?>
<!--
    日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出
    scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true
    scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。
    debug:属性为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。
-->
<configuration  scan="false" scanPeriod="120 seconds" debug="false">
    <contextName>kdts</contextName>
    <conversionRule converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" conversionWord="kEx" />

    <!-- name的值是变量的名称，value的值时变量定义的值。通过定义的值会被插入到logger上下文中。定义变量后，可以使“${}”来使用变量。 -->
    <property name="KDTS_LOG_PATH" value="${LOG_PATH:-${config.path:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/logs}" />
    <property name="KDTS_CONSOLE_LOG_PATTERN" value="${KDTS_CONSOLE_LOG_PATTERN:-%d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%15.15t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%kEx}}"/>
    <property name="KDTS_FILE_LOG_PATTERN" value="${KDTS_FILE_LOG_PATTERN:-%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n}" />

    <!--输出到控制台-->
    <!--该appender为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
    <appender name="KDTS_CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder><Pattern>${KDTS_CONSOLE_LOG_PATTERN}</Pattern><charset>UTF-8</charset></encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter"><level>debug</level></filter>
    </appender>

    <!--输出到文件-->
    <appender name="KDTS_ENV_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${KDTS_LOG_PATH}/env_logs.log</file>
        <encoder><pattern>${KDTS_FILE_LOG_PATTERN}</pattern><charset>UTF-8</charset></encoder>
        <!-- 滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy"><fileNamePattern>${KDTS_LOG_PATH}/debug/env_logs-%d{yyyy-MM-dd}.%i.log</fileNamePattern><maxHistory>20</maxHistory><!-- 文件保存天数 --><timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP"><maxFileSize>100MB</maxFileSize></timeBasedFileNamingAndTriggeringPolicy></rollingPolicy>
    </appender>

    <!-- DEBUG 日志 -->
    <appender name="KDTS_DEBUG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${KDTS_LOG_PATH}/kdts_debug.log</file>
        <encoder><charset>UTF-8</charset><pattern>${KDTS_FILE_LOG_PATTERN}</pattern></encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy"><fileNamePattern>${KDTS_LOG_PATH}/debug/log-debug-%d{yyyy-MM-dd}.%i.log</fileNamePattern><maxHistory>20</maxHistory><timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP"><maxFileSize>100MB</maxFileSize></timeBasedFileNamingAndTriggeringPolicy></rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter"><level>debug</level><onMatch>ACCEPT</onMatch><onMismatch>DENY</onMismatch></filter>
    </appender>

    <!-- INFO 日志 -->
    <appender name="KDTS_INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${KDTS_LOG_PATH}/kdts_info.log</file>
        <encoder><pattern>${KDTS_FILE_LOG_PATTERN}</pattern><charset>UTF-8</charset></encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy"><fileNamePattern>${KDTS_LOG_PATH}/info/log-info-%d{yyyy-MM-dd}.%i.log</fileNamePattern><maxHistory>20</maxHistory><timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP"><maxFileSize>100MB</maxFileSize></timeBasedFileNamingAndTriggeringPolicy></rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter"><level>info</level><onMatch>ACCEPT</onMatch><onMismatch>DENY</onMismatch></filter>
    </appender>

    <!-- WARN 日志 -->
    <appender name="KDTS_WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${KDTS_LOG_PATH}/kdts_warn.log</file>
        <encoder><pattern>${KDTS_FILE_LOG_PATTERN}</pattern><charset>UTF-8</charset></encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy"><fileNamePattern>${KDTS_LOG_PATH}/warn/log-warn-%d{yyyy-MM-dd}.%i.log</fileNamePattern><maxHistory>20</maxHistory><timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP"><maxFileSize>100MB</maxFileSize></timeBasedFileNamingAndTriggeringPolicy></rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter"><level>warn</level><onMatch>ACCEPT</onMatch><onMismatch>DENY</onMismatch></filter>
    </appender>

    <!-- ERROR 日志 -->
    <appender name="KDTS_ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${KDTS_LOG_PATH}/kdts_error.log</file>
        <encoder><pattern>${KDTS_FILE_LOG_PATTERN}</pattern><charset>UTF-8</charset></encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy"><fileNamePattern>${KDTS_LOG_PATH}/error/log-error-%d{yyyy-MM-dd}.%i.log</fileNamePattern><maxHistory>20</maxHistory><timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP"><maxFileSize>100MB</maxFileSize></timeBasedFileNamingAndTriggeringPolicy></rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter"><level>ERROR</level><onMatch>ACCEPT</onMatch><onMismatch>DENY</onMismatch></filter>
    </appender>

    <logger name="com.kingbase.kdts" level="INFO" additivity="false"><appender-ref ref="KDTS_CONSOLE" /><appender-ref ref="KDTS_DEBUG_FILE" /><appender-ref ref="KDTS_INFO_FILE" /><appender-ref ref="KDTS_WARN_FILE" /><appender-ref ref="KDTS_ERROR_FILE" /></logger>
    <logger name="c.k.k" level="INFO" additivity="false"><appender-ref ref="KDTS_CONSOLE" /><appender-ref ref="KDTS_DEBUG_FILE" /><appender-ref ref="KDTS_INFO_FILE" /><appender-ref ref="KDTS_WARN_FILE" /><appender-ref ref="KDTS_ERROR_FILE" /></logger>

    <root level="INFO"><appender-ref ref="KDTS_CONSOLE" /><appender-ref ref="KDTS_ENV_FILE" /></root>
</configuration>