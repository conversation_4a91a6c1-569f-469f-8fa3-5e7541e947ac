[{"sourceType": {"name": "geometry"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "linestring"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "multilinestring"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "multipoint"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "multipolygon"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "point"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "polygon"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "geometrycollection"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "BIT", "lengthMin": 1, "lengthMax": 1}, "targetType": {"name": "NUMBER", "lengthMin": 1, "lengthMax": 1}}, {"sourceType": {"name": "BIT", "lengthMin": 2, "lengthMax": 1000}, "targetType": {"name": "RAW", "lengthMin": 2, "lengthMax": 1000}}, {"sourceType": {"name": "BINARY", "lengthMin": 0, "lengthMax": 1000}, "targetType": {"name": "RAW", "lengthMin": 0, "lengthMax": 1000}}, {"sourceType": {"name": "VARBINARY", "lengthMin": 0, "lengthMax": 1000}, "targetType": {"name": "RAW", "lengthMin": 0, "lengthMax": 1000}}, {"sourceType": {"name": "year"}, "targetType": {"name": "number(4)"}}, {"sourceType": {"name": "set"}, "targetType": {"name": "varchar2"}}, {"sourceType": {"name": "enum"}, "targetType": {"name": "varchar2"}}, {"sourceType": {"name": "TINYINT"}, "targetType": {"name": "TINYINT"}}, {"sourceType": {"name": "TINYINT UNSIGNED"}, "targetType": {"name": "NUMBER(3)"}}, {"sourceType": {"name": "SMALLINT"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "SMALLINT UNSIGNED"}, "targetType": {"name": "NUMBER(5)"}}, {"sourceType": {"name": "MEDIUMINT"}, "targetType": {"name": "NUMBER(7)"}}, {"sourceType": {"name": "MEDIUMINT UNSIGNED"}, "targetType": {"name": "NUMBER(8)"}}, {"sourceType": {"name": "INT"}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "INT UNSIGNED"}, "targetType": {"name": "NUMBER(10)"}}, {"sourceType": {"name": "INTEGER"}, "targetType": {"name": "INTEGER"}}, {"sourceType": {"name": "INTEGER UNSIGNED"}, "targetType": {"name": "NUMBER(10)"}}, {"sourceType": {"name": "BIGINT"}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "BIGINT UNSIGNED"}, "targetType": {"name": "NUMBER(20)"}}]