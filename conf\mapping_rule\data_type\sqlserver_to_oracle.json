[{"sourceType": {"name": "timestamp"}, "targetType": {"name": "raw(8)"}}, {"sourceType": {"name": "datetimeoffset"}, "targetType": {"name": "timestamp with time zone"}}, {"sourceType": {"name": "datetime"}, "targetType": {"name": "timestamp(3)"}}, {"sourceType": {"name": "smalldatetime"}, "targetType": {"name": "timestamp(3)"}}, {"sourceType": {"name": "datetime2"}, "targetType": {"name": "timestamp(3)"}}, {"sourceType": {"name": "date"}, "targetType": {"name": "date"}}, {"sourceType": {"name": "time"}, "targetType": {"name": "varchar2(16)"}}, {"sourceType": {"name": "bit"}, "targetType": {"name": "number(1)"}}, {"sourceType": {"name": "bigint"}, "targetType": {"name": "number(19, 0)"}}, {"sourceType": {"name": "tinyint"}, "targetType": {"name": "number(3, 0)"}}, {"sourceType": {"name": "smallint"}, "targetType": {"name": "number(5, 0)"}}, {"sourceType": {"name": "int"}, "targetType": {"name": "number(10, 0)"}}, {"sourceType": {"name": "smallmoney"}, "targetType": {"name": "number(10, 4)"}}, {"sourceType": {"name": "money"}, "targetType": {"name": "number(19, 4)"}}, {"sourceType": {"name": "image"}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "uniqueidentifier"}, "targetType": {"name": "varchar2(40)"}}, {"sourceType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "decimal", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "number", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "varchar2", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 4001, "lengthMax": 2147483647}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "varchar2", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 4001, "lengthMax": 2147483647}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "binary", "precisionMin": 1, "precisionMax": 2000}, "targetType": {"name": "raw", "precisionMin": 1, "precisionMax": 2000}}, {"sourceType": {"name": "binary", "precisionMin": 2001, "precisionMax": 8000}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "varbinary", "precisionMin": 1, "precisionMax": 2000}, "targetType": {"name": "raw", "precisionMin": 1, "precisionMax": 2000}}, {"sourceType": {"name": "varbinary", "precisionMin": 2001, "precisionMax": 2147483647}, "targetType": {"name": "blob"}}, {"sourceType": {"name": "char", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "char", "lengthMin": 2001, "lengthMax": 2147483647}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "nchar", "lengthMin": 1, "lengthMax": 1000}, "targetType": {"name": "nchar", "lengthMin": 1, "lengthMax": 1000}}, {"sourceType": {"name": "nchar", "lengthMin": 1001, "lengthMax": 2147483647}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "text"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "ntext"}, "targetType": {"name": "nclob"}}, {"sourceType": {"name": "float"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "real"}, "targetType": {"name": "number"}}, {"sourceType": {"name": "xml"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "sql_variant"}, "targetType": {"name": "clob"}}, {"sourceType": {"name": "sysname"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(64)"}}, {"sourceType": {"name": "geometry"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "geography"}, "targetType": {"name": "geography"}}]