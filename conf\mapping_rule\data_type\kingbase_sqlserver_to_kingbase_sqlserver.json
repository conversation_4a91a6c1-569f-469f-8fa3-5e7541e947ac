[{"sourceType": {"name": "datetime"}, "targetType": {"name": "datetime"}}, {"sourceType": {"name": "binary", "precisionMin": 1, "precisionMax": 8000}, "targetType": {"name": "binary", "precisionMin": 1, "precisionMax": 8000}}, {"sourceType": {"name": "varbinary", "precisionMin": 1, "precisionMax": 8000}, "targetType": {"name": "varbinary", "precisionMin": 1, "precisionMax": 8000}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "varbinary", "precisionMin": 8001}, "targetType": {"name": "varbinary(max)"}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "rowversion"}, "targetType": {"name": "rowversion"}}, {"sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 4000}}, {"sourceType": {"name": "sysname"}, "targetType": {"name": "sysname"}}, {"sourceType": {"name": "uniqueidentifier"}, "targetType": {"name": "uniqueidentifier"}}, {"sourceType": {"name": "tinyint"}, "targetType": {"name": "tinyint"}}, {"sourceType": {"name": "float"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "float8"}, "targetType": {"name": "float8"}}, {"sourceType": {"name": "tinyint"}, "targetType": {"name": "tinyint"}}, {"sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "char", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 8000}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "int", "autoIncrement": true}, "targetType": {"name": "int identity"}, "regexMatchReplace": true, "regularExpressions": ["int"], "regularReplacements": ["int identity(${CURRENT_VALUE}, ${INCREMENTAL})"]}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "bigint", "autoIncrement": true}, "targetType": {"name": "bigint identity"}, "regexMatchReplace": true, "regularExpressions": ["bigint"], "regularReplacements": ["bigint identity(${CURRENT_VALUE}, ${INCREMENTAL})"]}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "tinyint", "autoIncrement": true}, "targetType": {"name": "tinyint identity"}, "regexMatchReplace": true, "regularExpressions": ["tinyint"], "regularReplacements": ["tinyint identity(${CURRENT_VALUE}, ${INCREMENTAL})"]}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "smallint", "autoIncrement": true}, "targetType": {"name": "smallint identity"}, "regexMatchReplace": true, "regularExpressions": ["smallint"], "regularReplacements": ["smallint identity(${CURRENT_VALUE}, ${INCREMENTAL})"]}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "NUMERIC", "autoIncrement": true, "precisionMin": 1}, "targetType": {"name": "numeric identity", "precisionMin": 1}, "regexMatchReplace": true, "regularExpressions": [".*"], "regularReplacements": ["NUMERIC(${PRECISION},${SCALE}) identity(${CURRENT_VALUE}, ${INCREMENTAL})"]}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "decimal", "autoIncrement": true, "precisionMin": 1}, "targetType": {"name": "decimal identity", "precisionMin": 1}, "regexMatchReplace": true, "regularExpressions": [".*"], "regularReplacements": ["decimal(${PRECISION},${SCALE}) identity(${CURRENT_VALUE}, ${INCREMENTAL})"]}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "smalldatetime"}, "targetType": {"name": "smalldatetime"}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "datetime2"}, "targetType": {"name": "datetime2"}, "regexMatchReplace": true, "regularExpressions": ["datetime2"], "regularReplacements": ["datetime2(${SCALE})"]}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "numeric", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "decimal", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}, "targetType": {"name": "decimal", "precisionMin": 0, "precisionMax": 38, "scaleMin": 0, "scaleMax": 38}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "nchar", "lengthMin": 1, "lengthMax": 4000}, "targetType": {"name": "nchar", "lengthMin": 1, "lengthMax": 4000}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 8001, "lengthMax": 2147483647}, "targetType": {"name": "varchar(max)"}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": 4001, "lengthMax": 2147483647}, "targetType": {"name": "nvar<PERSON><PERSON>(max)"}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": -4, "lengthMax": -4}, "targetType": {"name": "text"}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "n<PERSON><PERSON><PERSON>", "lengthMin": -4, "lengthMax": -4}, "targetType": {"name": "ntext"}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "varbinary", "lengthMin": -8, "lengthMax": -8, "precisionMin": -4, "precisionMax": -4}, "targetType": {"name": "image"}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "ntext"}, "targetType": {"name": "ntext"}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "smallmoney"}, "targetType": {"name": "smallmoney"}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "bit"}, "targetType": {"name": "bit"}}, {"minSourceDbVersion": "V9", "minTargetDbVersion": "V9", "sourceType": {"name": "xml"}, "targetType": {"name": "xml"}}]