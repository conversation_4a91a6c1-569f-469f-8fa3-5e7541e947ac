[{"minTargetDbVersion": "V8R6", "columnTypeIncludes": ["TIMESTAMP", "DATETIME"], "type": "RegexpReplace", "content": {"regexp": "CURRENT_TIMESTAMP", "replacement": "current_timestamp", "replaceType": "All"}}, {"minTargetDbVersion": "V8R6", "columnTypeIncludes": ["TIMESTAMP", "DATETIME"], "type": "RegexpReplace", "content": {"regexp": "SYSDATE", "replacement": "current_timestamp", "replaceType": "All"}}, {"minTargetDbVersion": "V8R6", "columnTypeIncludes": ["TIMESTAMP", "DATETIME"], "type": "RegexpReplace", "content": {"regexp": "NOW[(][)]", "replacement": "current_timestamp", "replaceType": "All"}}, {"minTargetDbVersion": "V8R6", "columnTypeIncludes": ["DATE"], "type": "RegexpReplace", "content": {"regexp": "NOW[(][)]", "replacement": "current_date", "replaceType": "All"}}, {"minTargetDbVersion": "V8R6", "columnTypeIncludes": ["TIME"], "type": "RegexpReplace", "content": {"regexp": "NOW[(][)]", "replacement": "current_time", "replaceType": "All"}}]