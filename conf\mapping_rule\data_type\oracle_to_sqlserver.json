[{"sourceType": {"name": "CHAR", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "char", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "VARCHAR", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127, "matchExpressions": "precision < scale"}, "targetType": {"name": "numeric(38, 0)"}}, {"sourceType": {"name": "NUMBER", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127}}, {"sourceType": {"name": "NUMBER"}, "targetType": {"name": "numeric(38, 0)"}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127, "matchExpressions": "precision < scale"}, "targetType": {"name": "numeric(38, 38)"}}, {"sourceType": {"name": "NUMERIC", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127}}, {"sourceType": {"name": "numeric"}, "targetType": {"name": "numeric(38, 38)"}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127, "matchExpressions": "precision < scale"}, "targetType": {"name": "numeric(38, 38)"}}, {"sourceType": {"name": "DECIMAL", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127}, "targetType": {"name": "numeric", "precisionMin": 1, "precisionMax": 38, "scaleMin": 0, "scaleMax": 127}}, {"sourceType": {"name": "DECIMAL"}, "targetType": {"name": "numeric(38, 38)"}}, {"sourceType": {"name": "BFILE"}, "targetType": {"name": "VARCHAR(1024)"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "IMAGE"}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "NCLOB"}, "targetType": {"name": "ntext"}}, {"sourceType": {"name": "BINARY_DOUBLE", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "float", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "BINARY_FLOAT", "lengthMin": 1, "lengthMax": 2000}, "targetType": {"name": "float", "lengthMin": 1, "lengthMax": 2000}}, {"sourceType": {"name": "FLOAT"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "DOUBLE"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "REAL"}, "targetType": {"name": "float"}}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "datetime2"}, "regexMatchReplace": true, "regularExpressions": ["TIMESTAMP(\\s*)(\\((?<precision1>\\d*)\\))"], "regularReplacements": ["datetime2"]}, {"sourceType": {"name": "TIMESTAMP WITH TIME ZONE"}, "targetType": {"name": "datetimeoffset"}, "regexMatchReplace": true, "regularExpressions": ["TIMESTAMP(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)WITH(\\s*)TIME(\\s*)ZONE"], "regularReplacements": ["datetimeoffset"]}, {"sourceType": {"name": "TIMESTAMP WITHOUT TIME ZONE"}, "targetType": {"name": "datetime2"}, "regexMatchReplace": true, "regularExpressions": ["TIMESTAMP(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)WITHOUT(\\s*)TIME(\\s*)ZONE"], "regularReplacements": ["datetime2"]}, {"sourceType": {"name": "DATE"}, "targetType": {"name": "datetime2"}}, {"sourceType": {"name": "TIMESTAMP WITH LOCAL TIME ZONE"}, "targetType": {"name": "datetime2"}, "regexMatchReplace": true, "regularExpressions": ["TIMESTAMP(\\s*)(\\((?<precision1>\\d*)\\))(\\s*)WITH(\\s*)LOCAL(\\s*)TIME(\\s*)ZONE"], "regularReplacements": ["datetime2"]}, {"sourceType": {"name": "NCHAR", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "NVARCHAR2", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "VARCHAR2", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "<PERSON><PERSON><PERSON>", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "BIT", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "BINARY", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "VARBIT", "lengthMin": 1, "lengthMax": 8000}, "targetType": {"name": "VARBINARY", "lengthMin": 1, "lengthMax": 8000}}, {"sourceType": {"name": "XMLTYPE"}, "targetType": {"name": "xml"}}, {"sourceType": {"name": "ROWID"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(4000)"}}, {"sourceType": {"name": "UROWID"}, "targetType": {"name": "<PERSON><PERSON><PERSON>(4000)"}}, {"sourceType": {"name": "LONG RAW"}, "targetType": {"name": "IMAGE"}}, {"sourceType": {"name": "RAW"}, "targetType": {"name": "IMAGE"}}, {"sourceType": {"name": "LONG"}, "targetType": {"name": "text"}}, {"sourceType": {"name": "INTERVAL YEAR"}, "targetType": {"name": "CHAR(12)"}}, {"sourceType": {"name": "INTERVAL DAY"}, "targetType": {"name": "CHAR(25)"}}, {"sourceType": {"name": "INTERVALDS"}, "targetType": {"name": "CHAR(25)"}}, {"sourceType": {"name": "INTERVALYM"}, "targetType": {"name": "CHAR(25)"}}, {"sourceType": {"name": "MDSYS.ST_GEOMETRY"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "MDSYS.SDO_GEOMETRY"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "MDSYS.SDO_DIM_ARRAY"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "MDSYS.SDO_DIM_ELEMENT"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "MDSYS.SDO_POINT_TYPE"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "MDSYS.SDO_NUMBER_ARRAY"}, "targetType": {"name": "geometry"}}, {"sourceType": {"name": "MDSYS.SDO_ELEM_INFO_ARRAY"}, "targetType": {"name": "geometry"}}]