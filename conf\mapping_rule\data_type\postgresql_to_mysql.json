[{"sourceType": {"name": "FLOAT"}, "targetType": {"name": "DOUBLE"}}, {"sourceType": {"name": "INT2"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "TINYINT"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "BIGINT"}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "OID"}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "REG"}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "TEXT"}, "targetType": {"name": "LONGTEXT"}}, {"sourceType": {"name": "XML"}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "INET"}, "targetType": {"name": "VARCHAR(30)"}}, {"sourceType": {"name": "VARBIT"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "BOOL"}, "targetType": {"name": "TINYINT(1)"}}, {"sourceType": {"name": "BYTEA"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "TINTERVAL"}, "targetType": {"name": "VARCHAR(50)"}}, {"sourceType": {"name": "BIT VARYING"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "BIT", "matchExpressions": "length <= 64"}, "targetType": {"name": "BIT", "lengthExpressions": "length"}}, {"sourceType": {"name": "BIT", "matchExpressions": "length > 64"}, "targetType": {"name": "BLOB"}}, {"sourceType": {"name": "SERIAL", "autoIncrement": true}, "targetType": {"name": "INT"}}, {"sourceType": {"name": "INT2"}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "BIGSERIAL", "autoIncrement": true}, "targetType": {"name": "BIGINT"}}, {"sourceType": {"name": "SMALLSERIAL", "autoIncrement": true}, "targetType": {"name": "SMALLINT"}}, {"sourceType": {"name": "CLOB"}, "targetType": {"name": "LONGTEXT"}}, {"sourceType": {"name": "NCLOB"}, "targetType": {"name": "LONGTEXT"}}, {"sourceType": {"name": "BLOB"}, "targetType": {"name": "LONGBLOB"}}, {"sourceType": {"name": "NAME"}, "targetType": {"name": "VARCHAR(64)"}}, {"sourceType": {"name": "TIMETZ"}, "targetType": {"name": "TIME"}, "regexMatchReplace": true, "regularExpressions": ["TIMETZ(.*)", "TIME(.*)WITH TIME ZONE"], "regularReplacements": ["TIME(${scale})", "TIME(${scale})"]}, {"sourceType": {"name": "timestamptz"}, "targetType": {"name": "DATETIME"}, "regexMatchReplace": true, "regularExpressions": ["timestamptz(.*)", "timestamp(.*)with time zone"], "regularReplacements": ["TIMESTAMP(${scale})", "TIMESTAMP(${scale})"]}, {"sourceType": {"name": "TIMESTAMP"}, "targetType": {"name": "DATETIME"}, "regexMatchReplace": true, "regularExpressions": ["TIMESTAMP(.*)WITHOUT TIMEZONE"], "regularReplacements": ["DATETIME(${scale})"]}, {"sourceType": {"name": "INTERVAL"}, "targetType": {"name": "VARCHAR(50)"}}, {"sourceType": {"name": "CIDR"}, "targetType": {"name": "VARCHAR(20)"}}, {"sourceType": {"name": "MONEY"}, "targetType": {"name": "VARCHAR(100)"}}, {"sourceType": {"name": "NUMERIC", "matchExpressions": "precision > 65 && scale  < 30"}, "targetType": {"name": "NUMERIC", "precisionExpressions": "65", "scaleExpressions": "scale"}}, {"sourceType": {"name": "NUMERIC", "matchExpressions": "precision > 65 && scale  >= 30"}, "targetType": {"name": "NUMERIC(65, 30)"}}, {"sourceType": {"name": "NUMERIC", "matchExpressions": "precision <= 65 && scale  < 30 && scale != 0"}, "targetType": {"name": "NUMERIC", "precisionExpressions": "precision", "scaleExpressions": "scale"}}, {"sourceType": {"name": "NUMERIC", "matchExpressions": "precision <= 65 && scale  >= 30"}, "targetType": {"name": "NUMERIC", "precisionExpressions": "precision", "scaleExpressions": "30"}}, {"sourceType": {"name": "BPCHAR", "matchExpressions": "length  > 255"}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "BPCHAR", "matchExpressions": "length  <= 255"}, "targetType": {"name": "CHAR", "lengthExpressions": "length"}}, {"sourceType": {"name": "CHAR", "matchExpressions": "length  > 255"}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "CHAR", "matchExpressions": "length  <= 255"}, "targetType": {"name": "CHAR", "lengthExpressions": "length"}}, {"sourceType": {"name": "VARCHAR", "matchExpressions": "length  > 8000"}, "targetType": {"name": "TEXT"}}, {"sourceType": {"name": "VARCHAR", "matchExpressions": "length  <= 8000"}, "targetType": {"name": "VARCHAR", "lengthExpressions": "length"}}, {"sourceType": {"name": "POINT"}, "targetType": {"name": "VARCHAR(46)"}}, {"sourceType": {"name": "LINE"}, "targetType": {"name": "VARCHAR(30)"}}, {"sourceType": {"name": "POLYGON"}, "targetType": {"name": "VARCHAR(197)"}}, {"sourceType": {"name": "BOX"}, "targetType": {"name": "VARCHAR(97)"}}, {"sourceType": {"name": "CIRCLE"}, "targetType": {"name": "VARCHAR(73)"}}, {"sourceType": {"name": "LSEG"}, "targetType": {"name": "VARCHAR(100)"}}, {"sourceType": {"name": "PATH"}, "targetType": {"name": "VARCHAR(140)"}}, {"sourceType": {"name": "RASTER"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "GEOGRAPHY"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "GEOMETRY"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "JSON"}, "targetType": {"name": "CLOB"}}, {"sourceType": {"name": "MACADDR"}, "targetType": {"name": "VARCHAR2(50)"}}, {"sourceType": {"name": "TSQUERY"}, "targetType": {"name": "VARCHAR2(2046)"}}, {"sourceType": {"name": "TSVECTOR"}, "targetType": {"name": "VARCHAR2(2046)"}}, {"sourceType": {"name": "UUID"}, "targetType": {"name": "VARCHAR2(2046)"}}, {"sourceType": {"name": "ABSTIME"}, "targetType": {"name": "TIMESTAMP"}}, {"sourceType": {"name": "RELTIME"}, "targetType": {"name": "VARCHAR2(50)"}}]